package com.recipe.service;

import com.recipe.entity.Favorite;
import com.recipe.entity.Recipe;
import com.recipe.entity.User;
import com.recipe.repository.FavoriteRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class FavoriteService {
    
    @Autowired
    private FavoriteRepository favoriteRepository;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private RecipeService recipeService;
    
    /**
     * 添加收藏
     */
    @Transactional
    public Favorite addFavorite(Long userId, Long recipeId) {
        // 获取用户
        User user = userService.getUserEntityById(userId);
        
        // 检查菜谱是否存在
        if (!recipeService.existsById(recipeId)) {
            throw new RuntimeException("菜谱不存在");
        }
        
        // 检查是否已经收藏
        if (favoriteRepository.existsByUserIdAndRecipeId(user.getId(), recipeId)) {
            throw new RuntimeException("已经收藏过该菜谱");
        }
        
        // 获取菜谱
        Recipe recipe = recipeService.getRecipeById(recipeId);
        
        // 创建收藏记录
        Favorite favorite = new Favorite(user, recipe);
        return favoriteRepository.save(favorite);
    }
    
    /**
     * 取消收藏
     */
    @Transactional
    public void removeFavorite(Long userId, Long recipeId) {
        // 获取用户
        User user = userService.getUserEntityById(userId);
        
        // 查找收藏记录
        Optional<Favorite> favoriteOptional = favoriteRepository.findByUserIdAndRecipeId(user.getId(), recipeId);
        if (!favoriteOptional.isPresent()) {
            throw new RuntimeException("未收藏该菜谱");
        }
        
        // 删除收藏记录
        favoriteRepository.delete(favoriteOptional.get());
    }
    
    /**
     * 获取用户收藏的菜谱列表（分页）
     */
    public Page<Recipe> getUserFavoriteRecipes(Long userId, int page, int size) {
        // 获取用户
        User user = userService.getUserEntityById(userId);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createTime"));
        return favoriteRepository.findRecipesByUserId(user.getId(), pageable);
    }
    
    /**
     * 获取用户收藏列表（包含收藏时间等信息）
     */
    public Page<Favorite> getUserFavorites(Long userId, int page, int size) {
        // 获取用户
        User user = userService.getUserEntityById(userId);
        
        Pageable pageable = PageRequest.of(page, size);
        return favoriteRepository.findByUserIdOrderByCreateTimeDesc(user.getId(), pageable);
    }
    
    /**
     * 检查用户是否收藏了某个菜谱
     */
    public boolean isFavorited(Long userId, Long recipeId) {
        try {
            User user = userService.getUserEntityById(userId);
            return favoriteRepository.existsByUserIdAndRecipeId(user.getId(), recipeId);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取用户收藏的菜谱ID列表
     */
    public List<Long> getUserFavoriteRecipeIds(Long userId) {
        User user = userService.getUserEntityById(userId);
        return favoriteRepository.findRecipeIdsByUserId(user.getId());
    }
    
    /**
     * 统计用户收藏数量
     */
    public long getUserFavoriteCount(Long userId) {
        User user = userService.getUserEntityById(userId);
        return favoriteRepository.countByUserId(user.getId());
    }
    
    /**
     * 统计菜谱被收藏数量
     */
    public long getRecipeFavoriteCount(Long recipeId) {
        return favoriteRepository.countByRecipeId(recipeId);
    }
    
    /**
     * 根据用户ID删除所有收藏（用户注销时使用）
     */
    @Transactional
    public void deleteAllUserFavorites(Long userId) {
        favoriteRepository.deleteByUserId(userId);
    }
    
    /**
     * 根据菜谱ID删除所有收藏（菜谱删除时使用）
     */
    @Transactional
    public void deleteAllRecipeFavorites(Long recipeId) {
        favoriteRepository.deleteByRecipeId(recipeId);
    }
}