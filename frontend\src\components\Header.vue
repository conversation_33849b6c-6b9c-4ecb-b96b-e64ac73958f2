<template>
  <div class="header">
    <el-menu
      :default-active="activeIndex"
      class="el-menu-demo"
      mode="horizontal"
      router
      background-color="#409EFF"
      text-color="#fff"
      active-text-color="#ffd04b">
      <el-menu-item index="/">
        <i class="el-icon-food"></i>
        <span>菜谱管理系统</span>
      </el-menu-item>
      <el-menu-item index="/recipes">
        <i class="el-icon-menu"></i>
        <span>菜谱列表</span>
      </el-menu-item>
      <el-menu-item index="/favorites" v-if="isUserLoggedIn">
        <i class="el-icon-star-on"></i>
        <span>我的收藏</span>
      </el-menu-item>
      
      <div class="user-menu">
        <template v-if="isUserLoggedIn">
          <el-dropdown trigger="click" @command="handleCommand" placement="bottom-end">
            <span class="el-dropdown-link">
              <el-avatar :size="32" :style="avatarStyle" class="user-avatar">
                {{ userInitial }}
              </el-avatar>
              <span class="username">{{ currentUsername }}</span>
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">
                <i class="el-icon-user"></i> 个人信息
              </el-dropdown-item>
              <el-dropdown-item command="change-password">
                <i class="el-icon-lock"></i> 修改密码
              </el-dropdown-item>
              <el-dropdown-item command="recipe-management">
                <i class="el-icon-document"></i> 菜谱管理
              </el-dropdown-item>
              <el-dropdown-item command="favorites">
                <i class="el-icon-star-on"></i> 我的收藏
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <i class="el-icon-switch-button"></i> 退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template v-else>
          <el-button type="text" @click="$router.push('/login')" class="login-btn">登录</el-button>
          <el-button type="text" @click="$router.push('/register')" class="register-btn">注册</el-button>
        </template>
      </div>
    </el-menu>
    
    <!-- 个人信息弹窗 -->
    <user-info-dialog :visible.sync="showUserInfoDialog" v-if="showUserInfoDialog"></user-info-dialog>
    
    <!-- 修改密码弹窗 -->
    <change-password-dialog :visible.sync="showChangePasswordDialog" v-if="showChangePasswordDialog"></change-password-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import UserInfoDialog from './UserInfoDialog.vue'
import ChangePasswordDialog from './ChangePasswordDialog.vue'

export default {
  name: 'Header',
  components: {
    UserInfoDialog,
    ChangePasswordDialog
  },
  data() {
    return {
      activeIndex: this.$route.path,
      showUserInfoDialog: false,
      showChangePasswordDialog: false
    }
  },
  computed: {
    ...mapGetters('auth', ['isLoggedIn', 'username']),
     
    // 双重验证登录状态 - 修改逻辑，直接从localStorage获取token
    isUserLoggedIn() {
      const token = localStorage.getItem('token')
      console.log('当前token状态:', !!token)
      return !!token
    },
    
    // 获取当前用户名 - 从localStorage获取用户信息
    currentUsername() {
      try {
        const userStr = localStorage.getItem('user')
        if (userStr) {
          const user = JSON.parse(userStr)
          return user.username || '用户'
        }
      } catch (e) {
        console.error('解析用户信息失败:', e)
      }
      return '用户'
    },
    
    // 获取用户名首字母
    userInitial() {
      return this.currentUsername.charAt(0).toUpperCase()
    },
    
    // 生成随机头像背景色
    avatarStyle() {
      // 根据用户名生成固定的颜色
      const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
      const index = this.currentUsername.charCodeAt(0) % colors.length
      return { backgroundColor: colors[index] }
    }
  },
  
  methods: {
    // 处理下拉菜单命令
    handleCommand(command) {
      console.log('Header处理命令:', command)
      
      switch (command) {
        case 'logout':
          this.handleLogout()
          break
        case 'profile':
          console.log('显示个人信息弹窗')
          this.showUserInfoDialog = true
          break
        case 'change-password':
          console.log('显示修改密码弹窗')
          this.showChangePasswordDialog = true
          break
        case 'recipe-management':
          this.$router.push('/recipe-management')
          break
        case 'favorites':
          this.$router.push('/favorites')
          break
        default:
          console.warn('未知的命令:', command)
      }
    },
    
    // 处理登出
    async handleLogout() {
      try {
        await this.$store.dispatch('auth/logout')
        this.$message.success('已成功退出登录')
        this.$router.push('/')
        // 强制刷新页面以确保状态更新
        setTimeout(() => {
          window.location.reload()
        }, 300)
      } catch (error) {
        console.error('登出失败:', error)
        this.$message.error('登出失败，请稍后重试')
      }
    }
  },
  
  watch: {
    // 监听路由变化
    $route(to) {
      this.activeIndex = to.path
    }
  },
  
  // 组件挂载后检查登录状态
  mounted() {
    console.log('Header组件挂载，检查登录状态:', this.isUserLoggedIn)
    // 如果有token但Vuex中没有用户信息，尝试从localStorage恢复
    if (this.isUserLoggedIn && !this.username) {
      try {
        const userStr = localStorage.getItem('user')
        if (userStr) {
          const user = JSON.parse(userStr)
          this.$store.commit('auth/set_user', user)
        }
      } catch (e) {
        console.error('恢复用户信息失败:', e)
      }
    }
  }
}
</script>

<style scoped>
.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.el-menu-demo {
  display: flex;
  align-items: center;
}

.user-menu {
  margin-left: auto;
  display: flex;
  align-items: center;
  padding-right: 20px;
}

.el-dropdown-link {
  cursor: pointer;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 60px;
}

.el-dropdown-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  margin-right: 8px;
  font-weight: bold;
}

.username {
  margin-right: 5px;
  font-size: 14px;
}

.login-btn, .register-btn {
  color: #fff !important;
  margin-left: 10px;
  font-size: 14px;
}

.login-btn:hover, .register-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 确保菜单项正确对齐 */
.el-menu--horizontal > .el-menu-item {
  height: 60px;
  line-height: 60px;
}
</style>