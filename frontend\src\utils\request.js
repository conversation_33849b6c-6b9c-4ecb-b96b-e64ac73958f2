import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: 'http://localhost:8080', // API的基础URL
  timeout: 15000 // 请求超时时间
})

// 模拟数据
const mockData = {
  recipes: [
    {
      id: 1,
      name: '宫保鸡丁',
      description: '经典川菜，麻辣鲜香，鸡肉嫩滑，花生酥脆',
      ingredients: '鸡胸肉、花生米、干辣椒、花椒、葱、姜、蒜',
      instructions: '1. 鸡肉切丁腌制\n2. 热锅下油炒花生米\n3. 爆炒鸡丁\n4. 调味炒制',
      imageUrl: 'https://via.placeholder.com/300x200?text=宫保鸡丁',
      createTime: '2024-01-15 10:30:00',
      updateTime: '2024-01-15 10:30:00'
    },
    {
      id: 2,
      name: '麻婆豆腐',
      description: '四川传统名菜，麻辣鲜香，豆腐嫩滑',
      ingredients: '嫩豆腐、牛肉末、豆瓣酱、花椒粉、葱花',
      instructions: '1. 豆腐切块焯水\n2. 炒制牛肉末\n3. 下豆瓣酱炒香\n4. 加豆腐调味',
      imageUrl: 'https://via.placeholder.com/300x200?text=麻婆豆腐',
      createTime: '2024-01-14 15:20:00',
      updateTime: '2024-01-14 15:20:00'
    },
    {
      id: 3,
      name: '红烧肉',
      description: '江南名菜，色泽红亮，肥而不腻，入口即化',
      ingredients: '五花肉、冰糖、生抽、老抽、料酒、葱、姜',
      instructions: '1. 五花肉切块焯水\n2. 炒糖色\n3. 下肉块炒制\n4. 加调料焖煮',
      imageUrl: 'https://via.placeholder.com/300x200?text=红烧肉',
      createTime: '2024-01-13 12:45:00',
      updateTime: '2024-01-13 12:45:00'
    },
    {
      id: 4,
      name: '西红柿鸡蛋',
      description: '家常菜经典，酸甜可口，营养丰富',
      ingredients: '西红柿、鸡蛋、葱花、盐、糖',
      instructions: '1. 鸡蛋打散炒制\n2. 西红柿切块\n3. 炒制西红柿出汁\n4. 加入鸡蛋调味',
      imageUrl: 'https://via.placeholder.com/300x200?text=西红柿鸡蛋',
      createTime: '2024-01-12 18:30:00',
      updateTime: '2024-01-12 18:30:00'
    }
  ]
}

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 直接从localStorage获取token，不依赖Vuex状态
    const token = localStorage.getItem('token')
    if (token) {
      console.log('请求添加token:', config.url)
      config.headers['Authorization'] = 'Bearer ' + token
    }
    return config
  },
  error => {
    console.log(error)
    Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    return response
  },
  error => {
    console.log('err: ' + error)
    
    // 处理网络错误或后端不可用的情况
    if (error.code === 'ECONNREFUSED' || error.message.includes('Network Error') || !error.response) {
      console.log('后端服务不可用，使用模拟数据')
      
      // 根据请求URL返回模拟数据
      const config = error.config
      if (config.url === '/api/recipes' && config.method === 'get') {
        // 修改模拟数据格式以匹配后端
        return Promise.resolve({ 
          data: {
            content: mockData,
            totalElements: mockData.length,
            totalPages: 1,
            number: 0,
            size: 10
          }
        })
      }
      else if (config.url.match(/\/api\/recipes\/\d+/) && config.method === 'get') {
        const id = parseInt(config.url.split('/').pop())
        const recipe = mockData.find(r => r.id === id)
        return Promise.resolve({ 
          data: recipe || null  // 直接返回菜谱对象，不包装在额外的结构中
        })
      } else if (config.url === '/api/recipes/search' && config.method === 'get') {
        // 修改搜索结果格式
        const searchResults = mockData.filter(r => 
          r.name.toLowerCase().includes((config.params?.name || '').toLowerCase())
        )
        return Promise.resolve({ 
          data: {
            content: searchResults,
            totalElements: searchResults.length,
            totalPages: 1,
            number: 0,
            size: 10
          }
        })
      } else if (config.url === '/api/recipes/latest' && config.method === 'get') {
        // 修改最新菜谱格式
        return Promise.resolve({ 
          data: {
            content: mockData.slice(0, 2),
            totalElements: 2,
            totalPages: 1,
            number: 0,
            size: 10
          }
        })
      } else {
        return Promise.resolve({ 
          data: {
            code: 200,
            message: '操作成功',
            data: null
          }
        })
      }
    }
    
    // 处理错误响应
    const { response } = error
    if (response) {
      // 401: 未授权
      if (response.status === 401) {
        Message({
          message: '登录状态已过期，请重新登录',
          type: 'error',
          duration: 5 * 1000
        })
        
        // 清除token并跳转到登录页面
        store.dispatch('auth/logout')
        router.push(`/login?redirect=${router.currentRoute.fullPath}`)
      } else {
        // 其他错误
        Message({
          message: response.data.message || '请求失败',
          type: 'error',
          duration: 5 * 1000
        })
      }
    } else {
      // 请求超时或网络错误
      Message({
        message: '网络错误，请检查您的网络连接',
        type: 'error',
        duration: 5 * 1000
      })
    }
    
    return Promise.reject(error)
  }
)

export default service