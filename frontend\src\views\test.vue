<template>
  <div class="test-container">
    <h1>登录状态调试页面</h1>
    
    <el-card class="debug-card">
      <div slot="header">
        <span>本地存储状态</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshStatus">刷新</el-button>
      </div>
      
      <div class="status-item">
        <strong>Token存在:</strong> {{ !!token }}
      </div>
      
      <div class="status-item" v-if="token">
        <strong>Token值:</strong> 
        <el-input type="textarea" :rows="2" v-model="token" readonly></el-input>
      </div>
      
      <div class="status-item">
        <strong>用户信息存在:</strong> {{ !!userStr }}
      </div>
      
      <div class="status-item" v-if="userStr">
        <strong>用户信息:</strong>
        <pre>{{ JSON.stringify(JSON.parse(userStr), null, 2) }}</pre>
      </div>
    </el-card>
    
    <el-card class="debug-card">
      <div slot="header">
        <span>Vuex状态</span>
      </div>
      
      <div class="status-item">
        <strong>isLoggedIn:</strong> {{ isLoggedIn }}
      </div>
      
      <div class="status-item">
        <strong>authStatus:</strong> {{ authStatus }}
      </div>
      
      <div class="status-item">
        <strong>用户名:</strong> {{ username }}
      </div>
      
      <div class="status-item" v-if="user">
        <strong>用户对象:</strong>
        <pre>{{ JSON.stringify(user, null, 2) }}</pre>
      </div>
    </el-card>
    
    <div class="action-buttons">
      <el-button type="primary" @click="testGetUserInfo">测试获取用户信息</el-button>
      <el-button type="danger" @click="clearStorage">清除本地存储</el-button>
      <el-button @click="forceRefresh">强制刷新页面</el-button>
    </div>
    
    <el-card class="debug-card" v-if="apiResult">
      <div slot="header">
        <span>API测试结果</span>
      </div>
      <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'TestPage',
  data() {
    return {
      token: localStorage.getItem('token') || '',
      userStr: localStorage.getItem('user') || '',
      apiResult: null
    }
  },
  computed: {
    ...mapGetters('auth', ['isLoggedIn', 'authStatus', 'username', 'user'])
  },
  methods: {
    refreshStatus() {
      this.token = localStorage.getItem('token') || ''
      this.userStr = localStorage.getItem('user') || ''
    },
    clearStorage() {
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      this.$store.commit('auth/logout')
      this.refreshStatus()
      this.$message.success('已清除本地存储和Vuex状态')
    },
    forceRefresh() {
      window.location.reload()
    },
    async testGetUserInfo() {
      try {
        this.apiResult = { status: 'loading' }
        const response = await this.$http.get('/api/users/info')
        this.apiResult = response.data
        this.$message.success('API请求成功')
      } catch (error) {
        console.error('API请求失败:', error)
        this.apiResult = { 
          error: true, 
          message: error.message,
          response: error.response ? error.response.data : null
        }
        this.$message.error('API请求失败: ' + error.message)
      }
    }
  }
}
</script>

<style scoped>
.test-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 0 20px;
}

.debug-card {
  margin-bottom: 20px;
}

.status-item {
  margin-bottom: 15px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

.action-buttons {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}
</style>