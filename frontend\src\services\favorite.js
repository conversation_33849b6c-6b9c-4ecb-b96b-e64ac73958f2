import request from '@/utils/request'

// 获取用户收藏的菜谱（支持分页）
export function getFavorites(page = 0, size = 10) {
  return request({
    url: '/api/favorites',
    method: 'get',
    params: { page, size }
  })
}

// 添加收藏
export function addFavorite(recipeId) {
  return request({
    url: '/api/favorites',
    method: 'post',
    data: { recipeId }
  })
}

// 取消收藏
export function removeFavorite(recipeId) {
  return request({
    url: `/api/favorites/${recipeId}`,
    method: 'delete'
  })
}

// 检查是否收藏
export function checkFavorite(recipeId) {
  return request({
    url: `/api/favorites/check/${recipeId}`,
    method: 'get'
  })
}

// 获取收藏ID列表
export function getFavoriteIds() {
  return request({
    url: '/api/favorites/ids',
    method: 'get'
  })
}

// 获取收藏数量
export function getFavoriteCount() {
  return request({
    url: '/api/favorites/count',
    method: 'get'
  })
}

// 获取菜谱被收藏数量
export function getRecipeFavoriteCount(recipeId) {
  return request({
    url: `/api/favorites/recipe/${recipeId}/count`,
    method: 'get'
  })
}