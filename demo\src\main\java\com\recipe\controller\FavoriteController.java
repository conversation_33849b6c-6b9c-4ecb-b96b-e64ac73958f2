package com.recipe.controller;

import com.recipe.dto.FavoriteRequest;
import com.recipe.entity.Recipe;
import com.recipe.service.FavoriteService;
import com.recipe.service.UserService;
import com.recipe.util.JwtUtil;
import com.recipe.util.ResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/favorites")
@CrossOrigin(origins = "*")
public class FavoriteController {
    
    @Autowired
    private FavoriteService favoriteService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 添加收藏
     */
    @PostMapping
    public Map<String, Object> addFavorite(
            @RequestHeader("Authorization") String token,
            @RequestBody FavoriteRequest request) {
        try {
            // Validate token
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseUtil.unauthorized("Please provide a valid authentication token");
            }
            
            String jwt = token.substring(7);
            if (!jwtUtil.isTokenValid(jwt)) {
                return ResponseUtil.unauthorized("Authentication token is invalid or expired");
            }
            
            String username = jwtUtil.getUsernameFromToken(jwt);
            Long userId = userService.getUserByUsername(username).getId();
            
            // Parameter validation
            if (request.getRecipeId() == null || request.getRecipeId() <= 0) {
                return ResponseUtil.badRequest("Invalid recipe ID");
            }
            
            favoriteService.addFavorite(userId, request.getRecipeId());
            return ResponseUtil.success("Favorite added successfully");
            
        } catch (RuntimeException e) {
            return ResponseUtil.badRequest(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("Failed to add favorite");
        }
    }
    
    /**
     * 取消收藏
     */
    @DeleteMapping("/{recipeId}")
    public Map<String, Object> removeFavorite(
            @RequestHeader("Authorization") String token,
            @PathVariable Long recipeId) {
        try {
            // 验证token
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseUtil.unauthorized("请提供有效的认证令牌");
            }
            
            String jwt = token.substring(7);
            if (!jwtUtil.isTokenValid(jwt)) {
                return ResponseUtil.unauthorized("认证令牌无效或已过期");
            }
            
            String username = jwtUtil.getUsernameFromToken(jwt);
            Long userId = userService.getUserByUsername(username).getId();
            
            // 参数验证
            if (recipeId == null || recipeId <= 0) {
                return ResponseUtil.badRequest("无效的菜谱ID");
            }
            
            favoriteService.removeFavorite(userId, recipeId);
            return ResponseUtil.success("取消收藏成功");
            
        } catch (RuntimeException e) {
            return ResponseUtil.badRequest(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("取消收藏失败");
        }
    }
    
    /**
     * 获取用户收藏的菜谱列表
     */
    @GetMapping
    public Map<String, Object> getUserFavorites(
            @RequestHeader("Authorization") String token,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            // 验证token
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseUtil.unauthorized("请提供有效的认证令牌");
            }
            
            String jwt = token.substring(7);
            if (!jwtUtil.isTokenValid(jwt)) {
                return ResponseUtil.unauthorized("认证令牌无效或已过期");
            }
            
            String username = jwtUtil.getUsernameFromToken(jwt);
            Long userId = userService.getUserByUsername(username).getId();
            
            // 参数验证
            if (page < 0) {
                return ResponseUtil.badRequest("页码不能小于0");
            }
            if (size <= 0 || size > 100) {
                return ResponseUtil.badRequest("每页大小必须在1-100之间");
            }
            
            Page<Recipe> favorites = favoriteService.getUserFavoriteRecipes(userId, page, size);
            
            return ResponseUtil.page(
                favorites.getContent(),
                favorites.getTotalElements(),
                favorites.getTotalPages(),
                favorites.getNumber(),
                favorites.getSize()
            );
            
        } catch (Exception e) {
            return ResponseUtil.error("获取收藏列表失败");
        }
    }
    
    /**
     * 检查用户是否收藏了某个菜谱
     */
    @GetMapping("/check/{recipeId}")
    public Map<String, Object> checkFavorite(
            @RequestHeader("Authorization") String token,
            @PathVariable Long recipeId) {
        try {
            // 验证token
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseUtil.unauthorized("请提供有效的认证令牌");
            }
            
            String jwt = token.substring(7);
            if (!jwtUtil.isTokenValid(jwt)) {
                return ResponseUtil.unauthorized("认证令牌无效或已过期");
            }
            
            String username = jwtUtil.getUsernameFromToken(jwt);
            Long userId = userService.getUserByUsername(username).getId();
            
            // 参数验证
            if (recipeId == null || recipeId <= 0) {
                return ResponseUtil.badRequest("无效的菜谱ID");
            }
            
            boolean isFavorited = favoriteService.isFavorited(userId, recipeId);
            return ResponseUtil.success("查询成功", Map.of("isFavorited", isFavorited));
            
        } catch (Exception e) {
            return ResponseUtil.error("查询收藏状态失败");
        }
    }
    
    /**
     * 获取用户收藏的菜谱ID列表
     */
    @GetMapping("/ids")
    public Map<String, Object> getUserFavoriteIds(
            @RequestHeader("Authorization") String token) {
        try {
            // 验证token
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseUtil.unauthorized("请提供有效的认证令牌");
            }
            
            String jwt = token.substring(7);
            if (!jwtUtil.isTokenValid(jwt)) {
                return ResponseUtil.unauthorized("认证令牌无效或已过期");
            }
            
            String username = jwtUtil.getUsernameFromToken(jwt);
            Long userId = userService.getUserByUsername(username).getId();
            
            List<Long> favoriteIds = favoriteService.getUserFavoriteRecipeIds(userId);
            return ResponseUtil.success("获取成功", favoriteIds);
            
        } catch (Exception e) {
            return ResponseUtil.error("获取收藏ID列表失败");
        }
    }
    
    /**
     * 获取用户收藏数量
     */
    @GetMapping("/count")
    public Map<String, Object> getUserFavoriteCount(
            @RequestHeader("Authorization") String token) {
        try {
            // 验证token
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseUtil.unauthorized("请提供有效的认证令牌");
            }
            
            String jwt = token.substring(7);
            if (!jwtUtil.isTokenValid(jwt)) {
                return ResponseUtil.unauthorized("认证令牌无效或已过期");
            }
            
            String username = jwtUtil.getUsernameFromToken(jwt);
            Long userId = userService.getUserByUsername(username).getId();
            
            long count = favoriteService.getUserFavoriteCount(userId);
            return ResponseUtil.success("获取成功", Map.of("count", count));
            
        } catch (Exception e) {
            return ResponseUtil.error("获取收藏数量失败");
        }
    }
    
    /**
     * 获取菜谱被收藏数量（公开接口）
     */
    @GetMapping("/recipe/{recipeId}/count")
    public Map<String, Object> getRecipeFavoriteCount(@PathVariable Long recipeId) {
        try {
            // 参数验证
            if (recipeId == null || recipeId <= 0) {
                return ResponseUtil.badRequest("无效的菜谱ID");
            }
            
            long count = favoriteService.getRecipeFavoriteCount(recipeId);
            return ResponseUtil.success("获取成功", Map.of("count", count));
            
        } catch (Exception e) {
            return ResponseUtil.error("获取菜谱收藏数量失败");
        }
    }
}