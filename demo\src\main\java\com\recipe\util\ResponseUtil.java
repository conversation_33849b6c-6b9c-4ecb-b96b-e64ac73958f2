package com.recipe.util;

import java.util.HashMap;
import java.util.Map;

public class ResponseUtil {
    
    private static final int SUCCESS_CODE = 200;
    private static final int ERROR_CODE = 500;
    private static final int UNAUTHORIZED_CODE = 401;
    private static final int NOT_FOUND_CODE = 404;
    private static final int BAD_REQUEST_CODE = 400;
    
    /**
     * 成功响应
     */
    public static Map<String, Object> success() {
        return success("操作成功", null);
    }
    
    public static Map<String, Object> success(Object data) {
        return success("操作成功", data);
    }
    
    public static Map<String, Object> success(String message, Object data) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", SUCCESS_CODE);
        result.put("message", message);
        result.put("data", data);
        return result;
    }
    
    /**
     * 错误响应
     */
    public static Map<String, Object> error(String message) {
        return error(ERROR_CODE, message);
    }
    
    public static Map<String, Object> error(int code, String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("message", message);
        result.put("data", null);
        return result;
    }
    
    /**
     * 未授权响应
     */
    public static Map<String, Object> unauthorized(String message) {
        return error(UNAUTHORIZED_CODE, message);
    }
    
    /**
     * 未找到响应
     */
    public static Map<String, Object> notFound(String message) {
        return error(NOT_FOUND_CODE, message);
    }
    
    /**
     * 请求参数错误响应
     */
    public static Map<String, Object> badRequest(String message) {
        return error(BAD_REQUEST_CODE, message);
    }
    
    /**
     * 分页响应
     */
    public static Map<String, Object> page(Object content, long totalElements, int totalPages, int number, int size) {
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("content", content);
        pageData.put("totalElements", totalElements);
        pageData.put("totalPages", totalPages);
        pageData.put("number", number);
        pageData.put("size", size);
        
        return success("获取成功", pageData);
    }
}