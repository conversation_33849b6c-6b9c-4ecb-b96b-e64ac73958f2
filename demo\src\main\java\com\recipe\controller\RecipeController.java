package com.recipe.controller;

import com.recipe.entity.Recipe;
import com.recipe.service.RecipeService;
import com.recipe.util.ResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/recipes")
@CrossOrigin(origins = "*")
public class RecipeController {
    
    @Autowired
    private RecipeService recipeService;
    
    /**
     * 获取菜谱列表（支持分页和搜索）
     */
    @GetMapping
    public Map<String, Object> getRecipes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword) {
        try {
            // 参数验证
            if (page < 0) {
                return ResponseUtil.badRequest("页码不能小于0");
            }
            if (size <= 0 || size > 100) {
                return ResponseUtil.badRequest("每页大小必须在1-100之间");
            }
            
            Page<Recipe> recipes = recipeService.getRecipes(page, size, keyword);
            
            return ResponseUtil.page(
                recipes.getContent(),
                recipes.getTotalElements(),
                recipes.getTotalPages(),
                recipes.getNumber(),
                recipes.getSize()
            );
            
        } catch (Exception e) {
            return ResponseUtil.error("获取菜谱列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取菜谱详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getRecipeById(@PathVariable Long id) {
        try {
            if (id == null || id <= 0) {
                return ResponseUtil.badRequest("无效的菜谱ID");
            }
            
            Recipe recipe = recipeService.getRecipeById(id);
            return ResponseUtil.success("获取成功", recipe);
            
        } catch (RuntimeException e) {
            return ResponseUtil.notFound(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("获取菜谱详情失败");
        }
    }
    
    /**
     * 创建新菜谱（管理员功能，暂时开放）
     */
    @PostMapping
    public Map<String, Object> createRecipe(@RequestBody Recipe recipe) {
        try {
            // 基本参数验证
            if (recipe.getName() == null || recipe.getName().trim().isEmpty()) {
                return ResponseUtil.badRequest("菜谱名称不能为空");
            }
            if (recipe.getName().length() > 100) {
                return ResponseUtil.badRequest("菜谱名称不能超过100个字符");
            }
            
            Recipe savedRecipe = recipeService.createRecipe(recipe);
            return ResponseUtil.success("创建成功", savedRecipe);
            
        } catch (RuntimeException e) {
            return ResponseUtil.badRequest(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("创建菜谱失败");
        }
    }
    
    /**
     * 更新菜谱（管理员功能，暂时开放）
     */
    @PutMapping("/{id}")
    public Map<String, Object> updateRecipe(@PathVariable Long id, @RequestBody Recipe recipeDetails) {
        try {
            if (id == null || id <= 0) {
                return ResponseUtil.badRequest("无效的菜谱ID");
            }
            
            // 基本参数验证
            if (recipeDetails.getName() != null && recipeDetails.getName().length() > 100) {
                return ResponseUtil.badRequest("菜谱名称不能超过100个字符");
            }
            
            Recipe updatedRecipe = recipeService.updateRecipe(id, recipeDetails);
            return ResponseUtil.success("更新成功", updatedRecipe);
            
        } catch (RuntimeException e) {
            return ResponseUtil.notFound(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("更新菜谱失败");
        }
    }
    
    /**
     * 删除菜谱（管理员功能，暂时开放）
     */
    @DeleteMapping("/{id}")
    public Map<String, Object> deleteRecipe(@PathVariable Long id) {
        try {
            if (id == null || id <= 0) {
                return ResponseUtil.badRequest("无效的菜谱ID");
            }
            
            recipeService.deleteRecipe(id);
            return ResponseUtil.success("删除成功");
            
        } catch (RuntimeException e) {
            return ResponseUtil.notFound(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("删除菜谱失败");
        }
    }
    
    /**
     * 根据菜名搜索菜谱
     */
    @GetMapping("/search")
    public Map<String, Object> searchRecipes(
            @RequestParam String name,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            if (name == null || name.trim().isEmpty()) {
                return ResponseUtil.badRequest("搜索关键词不能为空");
            }
            
            // 参数验证
            if (page < 0) {
                return ResponseUtil.badRequest("页码不能小于0");
            }
            if (size <= 0 || size > 100) {
                return ResponseUtil.badRequest("每页大小必须在1-100之间");
            }
            
            Page<Recipe> recipes = recipeService.searchRecipesByName(name.trim(), page, size);
            
            return ResponseUtil.page(
                recipes.getContent(),
                recipes.getTotalElements(),
                recipes.getTotalPages(),
                recipes.getNumber(),
                recipes.getSize()
            );
            
        } catch (Exception e) {
            return ResponseUtil.error("搜索菜谱失败");
        }
    }
    
    /**
     * 获取最新菜谱
     */
    @GetMapping("/latest")
    public Map<String, Object> getLatestRecipes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            // 参数验证
            if (page < 0) {
                return ResponseUtil.badRequest("页码不能小于0");
            }
            if (size <= 0 || size > 100) {
                return ResponseUtil.badRequest("每页大小必须在1-100之间");
            }
            
            Page<Recipe> recipes = recipeService.getLatestRecipes(page, size);
            
            return ResponseUtil.page(
                recipes.getContent(),
                recipes.getTotalElements(),
                recipes.getTotalPages(),
                recipes.getNumber(),
                recipes.getSize()
            );
            
        } catch (Exception e) {
            return ResponseUtil.error("获取最新菜谱失败");
        }
    }
}