<template>
  <div class="register-container">
    <div class="register-form">
      <div class="form-header">
        <h2>用户注册</h2>
        <p>加入菜谱管理系统，开始您的美食之旅</p>
      </div>
      
      <el-form
        ref="registerForm"
        :model="registerForm"
        :rules="registerRules"
        label-width="0px"
        class="register-form-content"
      >
        <el-form-item prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名"
            prefix-icon="el-icon-user"
            size="large"
            clearable
          ></el-input>
        </el-form-item>
        
        <el-form-item prop="email">
          <el-input
            v-model="registerForm.email"
            placeholder="请输入邮箱地址"
            prefix-icon="el-icon-message"
            size="large"
            clearable
          ></el-input>
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="el-icon-lock"
            size="large"
            show-password
          ></el-input>
        </el-form-item>
        
        <el-form-item prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请确认密码"
            prefix-icon="el-icon-lock"
            size="large"
            show-password
            @keyup.enter.native="handleRegister"
          ></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleRegister"
            class="register-btn"
          >
            {{ loading ? '注册中...' : '注册' }}
          </el-button>
        </el-form-item>
        
        <div class="form-footer">
          <span>已有账号？</span>
          <el-button type="text" @click="$router.push('/login')">
            立即登录
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Register',
  data() {
    // 确认密码验证规则
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.registerForm.password) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    
    return {
      loading: false,
      registerForm: {
        username: '',
        email: '',
        password: '',
        confirmPassword: ''
      },
      registerRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, message: '用户名只能包含字母、数字、下划线和中文', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
          { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  
  methods: {
    async handleRegister() {
      try {
        // 表单验证
        await this.$refs.registerForm.validate()
        
        this.loading = true
        
        // 准备注册数据
        const registerData = {
          username: this.registerForm.username,
          email: this.registerForm.email,
          password: this.registerForm.password
        }
        
        // 调用注册接口
        const result = await this.$store.dispatch('auth/register', registerData)
        console.log('注册结果:', result)
        
        this.$message.success('注册成功！欢迎加入菜谱管理系统')
        
        // 跳转到首页
        this.$router.push('/')
        
        // 强制刷新页面以确保状态更新
        setTimeout(() => {
          window.location.reload()
        }, 300)
        
      } catch (error) {
        console.error('注册失败:', error)
        
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(error.response.data.message)
        } else {
          this.$message.error('注册失败，请稍后重试')
        }
      } finally {
        this.loading = false
      }
    }
  },
  
  watch: {
    // 监听密码变化，重新验证确认密码
    'registerForm.password'() {
      if (this.registerForm.confirmPassword) {
        this.$refs.registerForm.validateField('confirmPassword')
      }
    }
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-form {
  width: 100%;
  max-width: 420px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.form-header {
  text-align: center;
  padding: 40px 30px 20px;
  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
  color: white;
}

.form-header h2 {
  margin: 0 0 10px;
  font-size: 28px;
  font-weight: 300;
}

.form-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.register-form-content {
  padding: 30px;
}

.register-btn {
  width: 100%;
  height: 45px;
  font-size: 16px;
  border-radius: 6px;
  background: #67C23A;
  border-color: #67C23A;
}

.register-btn:hover {
  background: #85ce61;
  border-color: #85ce61;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
  color: #666;
  font-size: 14px;
}

.form-footer .el-button--text {
  color: #67C23A;
  font-weight: 500;
  padding: 0;
  margin-left: 5px;
}

.form-footer .el-button--text:hover {
  color: #85ce61;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-container {
    padding: 10px;
  }
  
  .register-form {
    margin: 0;
  }
  
  .form-header {
    padding: 30px 20px 15px;
  }
  
  .form-header h2 {
    font-size: 24px;
  }
  
  .register-form-content {
    padding: 20px;
  }
}
</style>