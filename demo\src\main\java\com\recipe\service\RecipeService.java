package com.recipe.service;

import com.recipe.entity.Recipe;
import com.recipe.repository.RecipeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class RecipeService {
    
    @Autowired
    private RecipeRepository recipeRepository;
    
    /**
     * 获取菜谱列表（分页）
     */
    public Page<Recipe> getRecipes(int page, int size, String keyword) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createTime"));
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            // 根据关键词搜索
            return recipeRepository.findByKeyword(keyword.trim(), pageable);
        } else {
            // 获取所有菜谱
            return recipeRepository.findAll(pageable);
        }
    }
    
    /**
     * 根据ID获取菜谱详情
     */
    public Recipe getRecipeById(Long id) {
        Optional<Recipe> recipeOptional = recipeRepository.findById(id);
        if (!recipeOptional.isPresent()) {
            throw new RuntimeException("菜谱不存在");
        }
        
        return recipeOptional.get();
    }
    
    /**
     * 创建新菜谱
     */
    public Recipe createRecipe(Recipe recipe) {
        // 验证必填字段
        if (recipe.getName() == null || recipe.getName().trim().isEmpty()) {
            throw new RuntimeException("菜谱名称不能为空");
        }
        
        return recipeRepository.save(recipe);
    }
    
    /**
     * 更新菜谱
     */
    public Recipe updateRecipe(Long id, Recipe recipeDetails) {
        Optional<Recipe> recipeOptional = recipeRepository.findById(id);
        if (!recipeOptional.isPresent()) {
            throw new RuntimeException("菜谱不存在");
        }
        
        Recipe recipe = recipeOptional.get();
        
        // 更新字段
        if (recipeDetails.getName() != null && !recipeDetails.getName().trim().isEmpty()) {
            recipe.setName(recipeDetails.getName());
        }
        if (recipeDetails.getDescription() != null) {
            recipe.setDescription(recipeDetails.getDescription());
        }
        if (recipeDetails.getImageUrl() != null) {
            recipe.setImageUrl(recipeDetails.getImageUrl());
        }
        if (recipeDetails.getIngredients() != null) {
            recipe.setIngredients(recipeDetails.getIngredients());
        }
        if (recipeDetails.getSteps() != null) {
            recipe.setSteps(recipeDetails.getSteps());
        }
        
        return recipeRepository.save(recipe);
    }
    
    /**
     * 删除菜谱
     */
    public void deleteRecipe(Long id) {
        if (!recipeRepository.existsById(id)) {
            throw new RuntimeException("菜谱不存在");
        }
        
        recipeRepository.deleteById(id);
    }
    
    /**
     * 根据菜名搜索菜谱
     */
    public Page<Recipe> searchRecipesByName(String name, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createTime"));
        return recipeRepository.findByNameContainingIgnoreCase(name, pageable);
    }
    
    /**
     * 获取最新菜谱
     */
    public Page<Recipe> getLatestRecipes(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return recipeRepository.findAllByOrderByCreateTimeDesc(pageable);
    }
    
    /**
     * 检查菜谱是否存在
     */
    public boolean existsById(Long id) {
        return recipeRepository.existsById(id);
    }
}