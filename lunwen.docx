1.2 项目意义

随着人们生活水平的提高和饮食文化的多元化发展，烹饪已经成为现代人生活中不可或缺的一部分。菜谱管理系统作为一个集菜谱浏览、收藏和管理于一体的平台，能够为用户带来以下便利：

1.2.1 用户便利性

1. 信息集中化管理：系统将分散的菜谱信息进行集中整合，用户无需在多个平台间切换即可获取所需的烹饪知识，大大提高了信息获取效率。

2. 个性化服务：通过用户注册登录功能，系统能够记录用户的浏览和收藏习惯，为用户提供个性化的菜谱推荐，满足不同用户的饮食需求。

3. 便捷的收藏功能：用户可以将喜爱的菜谱添加到个人收藏列表，方便日后查阅和使用，解决了传统纸质菜谱易丢失、难管理的问题。

1.2.2 行业推动作用

1. 促进饮食文化传播：系统收录各类菜谱，有助于中华美食文化的传承和推广，同时也为用户了解不同地域的饮食特色提供了平台。

2. 推动餐饮行业数字化转型：作为餐饮行业数字化的一部分，菜谱管理系统的开发和应用，为餐饮行业的信息化建设提供了参考模式。

3. 数据价值挖掘：通过分析用户的浏览和收藏数据，可以洞察用户饮食偏好和趋势，为餐饮企业提供决策支持。

1.2.3 技术应用与创新亮点

1. 前后端分离架构：采用Vue.js + Spring Boot的前后端分离架构，提高了系统的可维护性和扩展性，同时也提升了用户的使用体验。

2. 安全认证机制：使用JWT（JSON Web Token）进行用户身份验证，保障了用户数据的安全性和系统的可靠性。

3. 响应式设计：前端采用Element UI组件库，实现了响应式布局，使系统能够适配不同设备，提升了用户体验。

4. 模块化开发：系统采用模块化的开发方式，各功能模块相对独立，便于后期的维护和扩展。

综上所述，菜谱管理系统不仅为用户提供了便捷的菜谱查询和管理服务，也为餐饮行业的数字化转型提供了有益探索，同时在技术应用上也体现了现代Web应用开发的特点和优势。

# 1.3 项目研究内容与目标

## 1.3.1 研究内容

本项目的主要研究内容包括以下几个方面：

1. **用户角色类型分析**
   - 普通用户：系统的主要使用者，可以浏览菜谱、注册登录、收藏菜谱等。
   - 管理员：负责系统的内容管理、用户管理等后台操作。

2. **功能模块开发**
   - **用户模块**：实现用户注册、登录、个人信息管理等功能。
   - **菜谱模块**：实现菜谱的展示、详情查看等功能。
   - **收藏模块**：实现用户对菜谱的收藏、取消收藏、查看收藏列表等功能。

3. **技术架构研究**
   - 前端技术栈：Vue.js 2.x、Vue Router、Vuex、Element UI、Axios等。
   - 后端技术栈：Spring Boot 2.7.x、MySQL 8.0、Spring Data JPA、Spring Security + JWT等。
   - 前后端交互：RESTful API设计与实现。

4. **系统性能优化**
   - 前端优化：启用gzip压缩，提高页面加载速度。
   - 后端优化：配置数据库连接池，提高数据访问效率。
   - 数据库优化：添加适当索引，提升查询性能。

## 1.3.2 项目目标

本项目的预期目标主要包括以下几个方面：

1. **功能目标**
   - 实现用户的注册、登录功能，支持基本的身份验证和授权。
   - 实现菜谱的浏览、详情查看功能，支持图文并茂的菜谱展示。
   - 实现菜谱的收藏、取消收藏、查看收藏列表功能。
   - 实现基本的用户个人中心功能（注：当前个人信息接口存在一定问题，属于项目开发过程中遇到的技术难点）。

2. **性能指标**
   - 页面加载时间：首页加载时间控制在3秒以内。
   - 系统响应时间：API接口响应时间控制在500ms以内。
   - 并发处理能力：支持至少50个用户同时在线操作。

3. **用户体验**
   - 界面设计简洁美观，操作流程清晰明了。
   - 提供友好的错误提示和操作反馈。
   - 支持主流浏览器，确保跨浏览器兼容性。

4. **安全性**
   - 用户密码采用BCrypt加密存储，保障用户账号安全。
   - 使用JWT进行身份验证，控制token有效期。
   - 实现输入参数验证和SQL注入防护，提高系统安全性。

通过以上目标的实现，本项目旨在构建一个功能完善、性能稳定、用户体验良好的菜谱管理系统，为用户提供便捷的菜谱浏览和管理服务。同时，项目开发过程中也将注重代码规范和开发效率，确保系统的可维护性和可扩展性。

# 2.3 功能模块概述与划分

本系统主要分为三个核心功能模块：用户管理模块、菜谱管理模块和收藏管理模块。各模块之间相互协作，共同构成完整的菜谱管理系统。

## 2.3.1 系统功能模块结构图

系统功能模块结构如下：

用户管理模块
├── 用户注册
├── 用户登录
├── 个人信息管理
└── 密码修改

菜谱管理模块
├── 菜谱列表浏览
├── 菜谱详情查看
└── 菜谱搜索

收藏管理模块
├── 添加收藏
├── 取消收藏
└── 收藏列表查看

## 2.3.2 模块功能概述

1. 用户管理模块：负责用户账户相关的所有功能，包括注册、登录、个人信息管理和密码修改等，是系统的基础功能模块，为其他模块提供用户身份认证支持。

2. 菜谱管理模块：系统的核心功能模块，提供菜谱的浏览、详情查看和搜索功能，使用户能够方便地获取所需的菜谱信息。

3. 收藏管理模块：为用户提供个性化服务的重要模块，允许用户收藏喜爱的菜谱，并进行管理，提升用户体验。

# 2.4 功能详细描述

## 2.4.1 用户管理模块

### 用户注册

功能说明：新用户通过填写注册表单创建账户
输入：用户名、邮箱、密码
输出：注册成功提示或错误信息
触发条件：用户点击注册按钮
使用角色：普通用户

### 用户登录

功能说明：已注册用户通过账号密码登录系统
输入：用户名/邮箱、密码
输出：登录成功跳转至首页或错误提示
触发条件：用户点击登录按钮
使用角色：普通用户

### 个人信息管理

功能说明：用户查看和修改个人资料
输入：用户名、邮箱等个人信息
输出：更新成功提示或错误信息
触发条件：用户进入个人信息页面并提交修改
使用角色：普通用户

### 密码修改

功能说明：用户修改账户密码
输入：原密码、新密码、确认新密码
输出：密码修改成功提示或错误信息
触发条件：用户提交密码修改表单
使用角色：普通用户

## 2.4.2 菜谱管理模块

### 菜谱列表浏览

功能说明：用户浏览系统中的菜谱列表
输入：分页参数、筛选条件（可选）
输出：菜谱列表数据
触发条件：用户访问菜谱列表页面
使用角色：普通用户

### 菜谱详情查看

功能说明：用户查看特定菜谱的详细信息
输入：菜谱ID
输出：菜谱详细信息（名称、材料、步骤、图片等）
触发条件：用户点击菜谱列表中的菜谱项
使用角色：普通用户

### 菜谱搜索

功能说明：用户通过关键词搜索菜谱
输入：搜索关键词
输出：符合条件的菜谱列表
触发条件：用户提交搜索表单
使用角色：普通用户

## 2.4.3 收藏管理模块

### 添加收藏

功能说明：用户将喜爱的菜谱添加到收藏列表
输入：菜谱ID
输出：收藏成功提示
触发条件：用户点击菜谱详情页的收藏按钮
使用角色：普通用户

### 取消收藏

功能说明：用户取消已收藏的菜谱
输入：菜谱ID
输出：取消收藏成功提示
触发条件：用户点击已收藏菜谱的取消收藏按钮
使用角色：普通用户

### 收藏列表查看

功能说明：用户查看自己的收藏菜谱列表
输入：分页参数
输出：用户收藏的菜谱列表
触发条件：用户访问收藏列表页面
使用角色：普通用户

# 2.5 非功能性需求

## 2.5.1 性能需求

1. 响应时间
   - 页面加载时间：首页加载时间不超过3秒
   - API响应时间：90%的API请求响应时间不超过500ms
   - 数据库查询时间：复杂查询不超过1秒

2. 并发用户数
   - 系统支持至少50个用户同时在线操作
   - 峰值时期能够处理至少100个并发请求

3. 系统吞吐量
   - 每秒处理至少20个事务
   - 每日处理数据量不少于10000条记录

## 2.5.2 安全性需求

1. 账户安全
   - 用户密码使用BCrypt算法加密存储
   - 登录失败超过5次，账户临时锁定15分钟
   - 敏感操作需要二次验证

2. 权限控制
   - 基于JWT的身份验证机制
   - 用户只能访问和修改自己的个人信息和收藏
   - API接口访问权限控制

3. 数据备份
   - 数据库每日自动备份
   - 备份数据保留至少30天

4. 访问日志
   - 记录用户登录、注销等关键操作
   - 记录API访问日志，包括访问时间、IP地址等信息

## 2.5.3 可用性需求

1. 用户界面设计
   - 采用响应式设计，适配不同设备屏幕
   - 界面风格统一，色彩搭配合理
   - 关键功能按钮醒目，位置合理

2. 操作易用性
   - 操作流程简单明了，减少用户操作步骤
   - 常用功能一键可达，减少页面跳转
   - 表单提供默认值和输入提示

3. 帮助提示
   - 关键功能提供操作引导
   - 首次使用提供功能介绍
   - 提供在线帮助文档

4. 错误处理
   - 表单提交前进行客户端验证
   - 服务器端错误返回友好提示
   - 系统异常时提供明确的错误信息和解决建议

# 3. 数据库设计

## 3.1 数据库映射

根据系统需求，将实体关系图映射为以下数据表：

### 用户表（user）
| 字段名 | 字段类型 | 约束 | 说明 |
| ------ | -------- | ---- | ---- |
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 用户ID |
| username | VARCHAR(50) | NOT NULL, UNIQUE | 用户名 |
| email | VARCHAR(100) | NOT NULL, UNIQUE | 邮箱 |
| password | VARCHAR(255) | NOT NULL | 密码（加密存储） |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 菜谱表（recipe）
| 字段名 | 字段类型 | 约束 | 说明 |
| ------ | -------- | ---- | ---- |
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 菜谱ID |
| title | VARCHAR(100) | NOT NULL | 菜谱名称 |
| description | TEXT | NULL | 菜谱描述 |
| ingredients | TEXT | NOT NULL | 食材列表 |
| steps | TEXT | NOT NULL | 烹饪步骤 |
| image_url | VARCHAR(255) | NULL | 菜谱图片URL |
| cooking_time | INT | NULL | 烹饪时间（分钟） |
| difficulty | VARCHAR(20) | NULL | 难度级别 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 收藏表（favorite）
| 字段名 | 字段类型 | 约束 | 说明 |
| ------ | -------- | ---- | ---- |
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 收藏ID |
| user_id | INT | NOT NULL, FOREIGN KEY | 用户ID，关联user表 |
| recipe_id | INT | NOT NULL, FOREIGN KEY | 菜谱ID，关联recipe表 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |

## 3.2 数据字典

### 用户表（user）
| 字段名 | 字段类型 | 字段描述 |
| ------ | -------- | -------- |
| id | INT | 用户的唯一标识符，自增长 |
| username | VARCHAR(50) | 用户登录名，不可重复 |
| email | VARCHAR(100) | 用户邮箱，用于找回密码等操作，不可重复 |
| password | VARCHAR(255) | 用户密码，使用BCrypt算法加密存储 |
| created_at | TIMESTAMP | 用户账号创建时间 |
| updated_at | TIMESTAMP | 用户信息最后更新时间 |

### 菜谱表（recipe）
| 字段名 | 字段类型 | 字段描述 |
| ------ | -------- | -------- |
| id | INT | 菜谱的唯一标识符，自增长 |
| title | VARCHAR(100) | 菜谱名称 |
| description | TEXT | 菜谱简介，包括口味、特点等 |
| ingredients | TEXT | 菜谱所需食材及用量，JSON格式存储 |
| steps | TEXT | 烹饪步骤，JSON格式存储 |
| image_url | VARCHAR(255) | 菜谱成品图片的URL地址 |
| cooking_time | INT | 烹饪所需时间，单位为分钟 |
| difficulty | VARCHAR(20) | 难度级别，如简单、中等、困难 |
| created_at | TIMESTAMP | 菜谱创建时间 |
| updated_at | TIMESTAMP | 菜谱最后更新时间 |

### 收藏表（favorite）
| 字段名 | 字段类型 | 字段描述 |
| ------ | -------- | -------- |
| id | INT | 收藏记录的唯一标识符，自增长 |
| user_id | INT | 收藏用户的ID，关联user表的id字段 |
| recipe_id | INT | 被收藏菜谱的ID，关联recipe表的id字段 |
| created_at | TIMESTAMP | 收藏创建时间 |

## 3.3 表间关系说明

本系统的数据库设计中包含三个主要表：用户表（user）、菜谱表（recipe）和收藏表（favorite）。它们之间的关系如下：

1. **用户表与收藏表（一对多关系）**
   - 一个用户可以收藏多个菜谱，因此用户表与收藏表之间是一对多关系
   - 在收藏表中，user_id作为外键关联到用户表的id字段
   - 关系表示：user(1) ---> favorite(n)

2. **菜谱表与收藏表（一对多关系）**
   - 一个菜谱可以被多个用户收藏，因此菜谱表与收藏表之间也是一对多关系
   - 在收藏表中，recipe_id作为外键关联到菜谱表的id字段
   - 关系表示：recipe(1) ---> favorite(n)

3. **用户表与菜谱表（多对多关系）**
   - 用户和菜谱之间是多对多关系，即一个用户可以收藏多个菜谱，一个菜谱也可以被多个用户收藏
   - 这种多对多关系通过收藏表（favorite）作为中间表来实现
   - 关系表示：user(n) <---> favorite <---> recipe(n)

### 关系图

```
+-------+       +-----------+       +--------+
| user  |       | favorite  |       | recipe |
+-------+       +-----------+       +--------+
| id    |<----->| user_id   |       | id     |
| ...   |       | recipe_id |<----->| ...    |
+-------+       +-----------+       +--------+
    1                 n                 1
     \               /                 /
      \             /                 /
       \           /                 /
        \         /                 /
         \       /                 /
          \     /                 /
           \   /                 /
            \ /                 /
             n                 n
```

通过这种设计，系统能够有效地管理用户、菜谱和收藏之间的关系，支持用户收藏菜谱、查看收藏列表等功能的实现。

## 3.1.2 模块结构图

本系统采用经典的三层架构设计，包括表示层、业务逻辑层和数据层，各层之间通过接口进行交互，实现了系统的高内聚低耦合。系统的整体架构如下图所示：

```
+----------------------------------+
|             表示层               |
|  +----------------------------+  |
|  |        Vue 页面组件        |  |
|  +----------------------------+  |
|  |        Vue Router          |  |
|  +----------------------------+  |
|  |        Vuex 状态管理       |  |
|  +----------------------------+  |
+----------------------------------+
              ↑     ↓
              |     |
+----------------------------------+
|           业务逻辑层             |
|  +----------------------------+  |
|  |      Controller 控制器     |  |
|  +----------------------------+  |
|  |       Service 服务层       |  |
|  +----------------------------+  |
+----------------------------------+
              ↑     ↓
              |     |
+----------------------------------+
|             数据层               |
|  +----------------------------+  |
|  |       Mapper 映射类        |  |
|  +----------------------------+  |
|  |          数据库            |  |
|  +----------------------------+  |
+----------------------------------+
```

### 表示层（前端）

表示层采用Vue.js框架实现，主要包括以下组件：

1. **Vue页面组件**：负责用户界面的展示和交互，包括登录、注册、菜谱列表、菜谱详情、收藏管理等页面。

2. **Vue Router**：负责前端路由管理，实现页面间的跳转和导航。

3. **Vuex状态管理**：负责管理应用的全局状态，如用户登录状态、菜谱数据等。

### 业务逻辑层（后端）

业务逻辑层采用Spring Boot框架实现，主要包括以下组件：

1. **Controller控制器**：负责接收和处理前端请求，调用相应的Service服务，并返回处理结果。

2. **Service服务层**：负责实现业务逻辑，如用户认证、菜谱管理、收藏管理等。

### 数据层（后端）

数据层负责数据的持久化和访问，主要包括以下组件：

1. **Mapper映射类**：基于MyBatis框架，负责SQL语句的映射和执行，实现对数据库的增删改查操作。

2. **数据库**：使用MySQL数据库存储系统数据，包括用户信息、菜谱信息、收藏关系等。

## 3.1.3 模块功能与职责说明

### 表示层模块

1. **用户界面模块**
   - 功能：提供用户交互界面，包括登录、注册、菜谱浏览、收藏管理等。
   - 内部处理：表单验证、数据展示、用户交互事件处理。
   - 调用关系：通过API调用业务逻辑层的Controller接口。

2. **路由模块**
   - 功能：管理前端页面路由，实现页面跳转和导航。
   - 内部处理：路由配置、路由守卫、路由参数处理。
   - 调用关系：与用户界面模块和状态管理模块交互。

3. **状态管理模块**
   - 功能：管理应用全局状态，如用户登录状态、菜谱数据等。
   - 内部处理：状态更新、状态持久化、状态共享。
   - 调用关系：与用户界面模块交互，通过API调用业务逻辑层。

### 业务逻辑层模块

1. **用户管理模块**
   - 功能：处理用户相关业务，如注册、登录、个人信息管理等。
   - 内部处理：用户认证、密码加密、Token生成与验证。
   - 调用关系：调用数据层的用户Mapper，被表示层调用。

2. **菜谱管理模块**
   - 功能：处理菜谱相关业务，如菜谱列表获取、菜谱详情查询等。
   - 内部处理：菜谱数据处理、分页查询、关键词搜索。
   - 调用关系：调用数据层的菜谱Mapper，被表示层调用。

3. **收藏管理模块**
   - 功能：处理收藏相关业务，如添加收藏、取消收藏、查看收藏列表等。
   - 内部处理：收藏状态管理、用户收藏关系处理。
   - 调用关系：调用数据层的收藏Mapper，被表示层调用。

### 数据层模块

1. **用户数据访问模块**
   - 功能：实现用户数据的持久化和访问。
   - 内部处理：用户表的增删改查操作。
   - 调用关系：被业务逻辑层的用户管理模块调用。

2. **菜谱数据访问模块**
   - 功能：实现菜谱数据的持久化和访问。
   - 内部处理：菜谱表的增删改查操作。
   - 调用关系：被业务逻辑层的菜谱管理模块调用。

3. **收藏数据访问模块**
   - 功能：实现收藏数据的持久化和访问。
   - 内部处理：收藏表的增删改查操作。
   - 调用关系：被业务逻辑层的收藏管理模块调用。

## 3.1.4 模块-功能映射关系

下表列出了系统各模块与数据流图（DFD）中功能过程的映射关系：

| 模块名称 | 所属层次 | 对应DFD过程 |
| ------- | ------- | ---------- |
| 用户界面模块 | 表示层 | 用户注册、用户登录、个人信息管理、密码修改 |
| 路由模块 | 表示层 | 页面导航与跳转 |
| 状态管理模块 | 表示层 | 用户状态管理、数据缓存 |
| 用户管理模块 | 业务逻辑层 | 用户注册处理、用户认证、个人信息更新 |
| 菜谱管理模块 | 业务逻辑层 | 菜谱列表获取、菜谱详情查询、菜谱搜索 |
| 收藏管理模块 | 业务逻辑层 | 添加收藏、取消收藏、收藏列表查询 |
| 用户数据访问模块 | 数据层 | 用户数据存储与检索 |
| 菜谱数据访问模块 | 数据层 | 菜谱数据存储与检索 |
| 收藏数据访问模块 | 数据层 | 收藏关系存储与检索 |

# 3.2 模块接口设计

## 3.2.1 主要接口列表

以下是系统主要模块的对外接口列表，采用函数式接口说明表的格式：

### 用户管理接口

| 接口名称 | 功能描述 | 请求方式 | 请求URL | 返回类型 |
| ------- | ------- | ------- | ------- | ------- |
| register | 用户注册 | POST | /api/auth/register | ResponseEntity<AuthResponse> |
| login | 用户登录 | POST | /api/auth/login | ResponseEntity<AuthResponse> |
| getUserInfo | 获取用户信息 | GET | /api/auth/user | ResponseEntity<UserDTO> |
| updateUserInfo | 更新用户信息 | PUT | /api/auth/user | ResponseEntity<UserDTO> |
| changePassword | 修改密码 | PUT | /api/auth/password | ResponseEntity<String> |

### 菜谱管理接口

| 接口名称 | 功能描述 | 请求方式 | 请求URL | 返回类型 |
| ------- | ------- | ------- | ------- | ------- |
| getRecipeList | 获取菜谱列表 | GET | /api/recipes | ResponseEntity<Page<RecipeDTO>> |
| getRecipeById | 获取菜谱详情 | GET | /api/recipes/{id} | ResponseEntity<RecipeDTO> |
| searchRecipes | 搜索菜谱 | GET | /api/recipes/search | ResponseEntity<List<RecipeDTO>> |

### 收藏管理接口

| 接口名称 | 功能描述 | 请求方式 | 请求URL | 返回类型 |
| ------- | ------- | ------- | ------- | ------- |
| addFavorite | 添加收藏 | POST | /api/favorites | ResponseEntity<FavoriteDTO> |
| removeFavorite | 取消收藏 | DELETE | /api/favorites/{recipeId} | ResponseEntity<String> |
| getFavorites | 获取收藏列表 | GET | /api/favorites | ResponseEntity<List<RecipeDTO>> |
| checkFavorite | 检查是否已收藏 | GET | /api/favorites/check/{recipeId} | ResponseEntity<Boolean> |

## 3.2.2 接口参数说明

### 用户管理接口参数

#### 1. 用户注册接口

**请求参数：**

| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| username | String | 是 | 用户名，长度5-20个字符 |
| email | String | 是 | 邮箱地址，符合邮箱格式 |
| password | String | 是 | 密码，长度8-20个字符 |

**返回参数：**

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| token | String | JWT认证令牌 |
| user | UserDTO | 用户信息对象 |
| message | String | 操作结果消息 |

#### 2. 用户登录接口

**请求参数：**

| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| username | String | 是 | 用户名或邮箱 |
| password | String | 是 | 密码 |

**返回参数：**

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| token | String | JWT认证令牌 |
| user | UserDTO | 用户信息对象 |
| message | String | 操作结果消息 |

### 菜谱管理接口参数

#### 1. 获取菜谱列表接口

**请求参数：**

| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| page | Integer | 否 | 页码，默认为0 |
| size | Integer | 否 | 每页条数，默认为10 |
| sort | String | 否 | 排序字段，默认为id |

**返回参数：**

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| content | List<RecipeDTO> | 菜谱列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |
| size | Integer | 每页条数 |
| number | Integer | 当前页码 |

#### 2. 搜索菜谱接口

**请求参数：**

| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| keyword | String | 是 | 搜索关键词 |
| page | Integer | 否 | 页码，默认为0 |
| size | Integer | 否 | 每页条数，默认为10 |

**返回参数：**

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| content | List<RecipeDTO> | 菜谱列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |
| size | Integer | 每页条数 |
| number | Integer | 当前页码 |

### 收藏管理接口参数

#### 1. 添加收藏接口

**请求参数：**

| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| recipeId | Integer | 是 | 菜谱ID |

**返回参数：**

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| id | Integer | 收藏ID |
| userId | Integer | 用户ID |
| recipeId | Integer | 菜谱ID |
| createdAt | LocalDateTime | 收藏时间 |
| message | String | 操作结果消息 |

#### 2. 获取收藏列表接口

**请求参数：**

| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| page | Integer | 否 | 页码，默认为0 |
| size | Integer | 否 | 每页条数，默认为10 |

**返回参数：**

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| content | List<RecipeDTO> | 收藏的菜谱列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |
| size | Integer | 每页条数 |
| number | Integer | 当前页码 |

# 3.3 数据库逻辑结构设计

## 3.3.1 ER图转换与表结构设计

根据系统需求分析和功能设计，我们将实体关系图(ER图)转换为具体的数据库表结构。本系统主要包含三个核心实体：用户(User)、菜谱(Recipe)和收藏(Favorite)。

### 用户表(user)

```sql
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 菜谱表(recipe)

```sql
CREATE TABLE `recipe` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `description` text,
  `ingredients` text NOT NULL,
  `steps` text NOT NULL,
  `image_url` varchar(255),
  `cooking_time` int(11),
  `difficulty` varchar(20),
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_title` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 收藏表(favorite)

```sql
CREATE TABLE `favorite` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `recipe_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_recipe` (`user_id`, `recipe_id`),
  KEY `fk_favorite_recipe` (`recipe_id`),
  CONSTRAINT `fk_favorite_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_favorite_recipe` FOREIGN KEY (`recipe_id`) REFERENCES `recipe` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 3.3.2 数据表字段说明

### 用户表(user)字段说明

| 字段名 | 类型 | 约束 | 说明 |
| ------ | ---- | ---- | ---- |
| id | int(11) | 主键，自增 | 用户ID，唯一标识 |
| username | varchar(50) | 非空，唯一 | 用户名 |
| password | varchar(100) | 非空 | 密码（加密存储） |
| email | varchar(100) | 非空，唯一 | 电子邮箱 |
| created_at | datetime | 非空 | 创建时间 |
| updated_at | datetime | 非空 | 更新时间 |

### 菜谱表(recipe)字段说明

| 字段名 | 类型 | 约束 | 说明 |
| ------ | ---- | ---- | ---- |
| id | int(11) | 主键，自增 | 菜谱ID，唯一标识 |
| title | varchar(100) | 非空 | 菜谱标题 |
| description | text | 可空 | 菜谱描述 |
| ingredients | text | 非空 | 食材清单 |
| steps | text | 非空 | 烹饪步骤 |
| image_url | varchar(255) | 可空 | 菜谱图片URL |
| cooking_time | int(11) | 可空 | 烹饪时间（分钟） |
| difficulty | varchar(20) | 可空 | 难度级别 |
| created_at | datetime | 非空 | 创建时间 |
| updated_at | datetime | 非空 | 更新时间 |

### 收藏表(favorite)字段说明

| 字段名 | 类型 | 约束 | 说明 |
| ------ | ---- | ---- | ---- |
| id | int(11) | 主键，自增 | 收藏ID，唯一标识 |
| user_id | int(11) | 外键，非空 | 用户ID，关联user表 |
| recipe_id | int(11) | 外键，非空 | 菜谱ID，关联recipe表 |
| created_at | datetime | 非空 | 收藏时间 |

## 3.3.3 表间关系与约束设计

本系统中的表间关系主要包括：

1. **用户与收藏的关系**：一对多关系
   - 一个用户可以有多个收藏记录
   - 每个收藏记录只属于一个用户
   - 关系约束：在收藏表(favorite)中，user_id作为外键关联用户表(user)的id字段
   - 级联操作：当删除用户时，级联删除该用户的所有收藏记录

2. **菜谱与收藏的关系**：一对多关系
   - 一个菜谱可以被多个用户收藏
   - 每个收藏记录只对应一个菜谱
   - 关系约束：在收藏表(favorite)中，recipe_id作为外键关联菜谱表(recipe)的id字段
   - 级联操作：当删除菜谱时，级联删除该菜谱的所有收藏记录

3. **用户与菜谱的关系**：多对多关系
   - 一个用户可以收藏多个菜谱
   - 一个菜谱可以被多个用户收藏
   - 这种多对多关系通过收藏表(favorite)作为中间表来实现

### 关系图示

```
+-------+       +-----------+       +--------+
| user  |       | favorite  |       | recipe |
+-------+       +-----------+       +--------+
| id    |<----->| user_id   |       | id     |
| ...   |       | recipe_id |<----->| ...    |
+-------+       +-----------+       +--------+
    1                 n                 1
     \               /                 /
      \             /                 /
       \           /                 /
        \         /                 /
         \       /                 /
          \     /                 /
           \   /                 /
            \ /                 /
             n                 n
```

### 约束说明

1. **主键约束**：
   - 每个表都有自己的主键(id)，确保记录的唯一性

2. **唯一约束**：
   - 用户表的username和email字段设置为唯一，防止重复注册
   - 收藏表的user_id和recipe_id组合设置为唯一，防止用户重复收藏同一菜谱

3. **外键约束**：
   - 收藏表的user_id关联用户表的id
   - 收藏表的recipe_id关联菜谱表的id

4. **级联删除**：
   - 当删除用户或菜谱时，相关的收藏记录会自动删除

# 3.4 系统技术架构初步设想

## 3.4.1 前端技术选型

本系统前端采用现代化的Web开发技术栈，主要包括：

1. **核心框架**：Vue.js 2.x
   - 采用组件化开发方式，提高代码复用性和可维护性
   - 利用虚拟DOM技术，提升页面渲染性能
   - 响应式数据绑定，简化开发流程

2. **UI组件库**：Element UI
   - 提供丰富的UI组件，如表单、表格、导航等
   - 符合现代设计风格，美观易用
   - 支持响应式布局，适配不同设备

3. **状态管理**：Vuex
   - 集中管理应用状态，如用户登录信息、菜谱数据等
   - 提供可预测的状态变更机制，便于调试和维护
   - 支持模块化组织，适合大型应用开发

4. **路由管理**：Vue Router
   - 实现单页应用的路由功能，支持页面间无刷新跳转
   - 提供路由守卫机制，便于实现权限控制
   - 支持路由懒加载，优化首屏加载速度

5. **HTTP请求**：Axios
   - 基于Promise的HTTP客户端，用于与后端API通信
   - 支持请求和响应拦截器，便于统一处理认证和错误
   - 支持请求取消和超时设置，提升用户体验

## 3.4.2 后端开发框架

后端采用Java生态系统的主流技术栈，主要包括：

1. **核心框架**：Spring Boot 2.7.x
   - 简化Spring应用的初始搭建和开发过程
   - 内置Tomcat服务器，便于部署和运行
   - 提供丰富的starter模块，减少配置工作

2. **API风格**：RESTful API
   - 采用REST架构风格设计API接口
   - 使用HTTP标准方法（GET、POST、PUT、DELETE等）表示操作
   - 返回JSON格式数据，便于前端处理

3. **数据访问**：Spring Data JPA
   - 简化数据库访问操作，减少样板代码
   - 提供丰富的查询方法，支持方法名查询、JPQL和原生SQL
   - 支持分页和排序功能，便于实现列表展示

4. **安全框架**：Spring Security + JWT
   - 实现用户认证和授权功能
   - 使用JWT（JSON Web Token）进行无状态身份验证
   - 保护API接口，防止未授权访问

5. **数据验证**：Hibernate Validator
   - 实现请求参数的验证功能
   - 提供丰富的验证注解，如@NotNull、@Size、@Email等
   - 减少手动编写验证代码，提高开发效率

## 3.4.3 数据存储方案

本系统采用MySQL作为主要的数据存储方案：

1. **数据库**：MySQL 8.0
   - 成熟稳定的关系型数据库，适合存储结构化数据
   - 支持事务处理，确保数据一致性
   - 提供完善的索引机制，优化查询性能
   - 支持外键约束，维护数据完整性

2. **连接池**：HikariCP
   - Spring Boot默认的数据库连接池
   - 高性能、轻量级的JDBC连接池
   - 提供连接池监控和管理功能

3. **缓存策略**：
   - 使用Spring Cache抽象层实现方法级缓存
   - 对热点数据（如常用菜谱列表）进行缓存，减轻数据库压力
   - 实现缓存自动更新和失效机制，保证数据一致性

## 3.4.4 部署方式说明

本系统的部署将分为两个阶段：

### 开发与测试阶段

1. **本地开发环境**
   - 前端：Node.js + npm开发服务器
   - 后端：内嵌Tomcat的Spring Boot应用
   - 数据库：本地MySQL实例
   - 开发工具：IntelliJ IDEA（后端）、VS Code（前端）

2. **测试环境**
   - 前端：打包为静态资源，部署到本地Nginx服务器
   - 后端：打包为JAR文件，使用Java命令直接运行
   - 数据库：本地或专用测试服务器上的MySQL实例
   - 测试工具：JUnit（单元测试）、Postman（API测试）

### 生产部署阶段

1. **服务器环境**
   - 云服务提供商：阿里云ECS（弹性计算服务）
   - 操作系统：CentOS 7或Ubuntu 20.04 LTS
   - 配置要求：2核4G内存（最低配置），50GB存储空间

2. **应用部署**
   - 前端：打包为静态资源，部署到Nginx服务器
   - 后端：使用Docker容器化部署Spring Boot应用
   - 数据库：阿里云RDS MySQL服务或自建MySQL实例
   - 文件存储：阿里云OSS（对象存储服务），用于存储菜谱图片

3. **运维管理**
   - 使用Jenkins实现持续集成和部署
   - 使用ELK Stack（Elasticsearch、Logstash、Kibana）进行日志管理
   - 使用Prometheus + Grafana进行系统监控
   - 定期数据备份和恢复演练，确保数据安全

# 4. 系统实现

## 4.1 开发环境与工具

*   **操作系统**：Windows 10 / macOS / Linux
*   **前端开发工具**：Visual Studio Code
*   **前端技术栈**：Vue.js 2.x, Element UI, Vuex, Vue Router, Axios
*   **后端开发工具**：IntelliJ IDEA
*   **后端技术栈**：Spring Boot 2.7.x, Spring Security, JWT, Spring Data JPA, Hibernate Validator
*   **数据库**：MySQL 8.0
*   **版本控制**：Git
*   **项目管理**：Maven
*   **接口测试工具**：Postman

## 4.2 系统项目结构概览

### 4.2.1 前端项目结构说明

前端项目基于 Vue CLI 创建，采用模块化的方式组织代码，主要目录结构如下：

```
frontend/
├── public/                     # 静态资源，如index.html
│   └── index.html
├── src/
│   ├── assets/                 # 存放图片、样式等静态资源
│   ├── components/             # 可复用的UI组件
│   │   ├── RecipeCard.vue      # 菜谱卡片组件
│   │   └── Navbar.vue          # 导航栏组件
│   ├── views/                  # 页面级组件
│   │   ├── Home.vue            # 首页
│   │   ├── Login.vue           # 登录页
│   │   ├── Register.vue        # 注册页
│   │   ├── RecipeDetail.vue    # 菜谱详情页
│   │   └── UserProfile.vue     # 用户个人中心
│   ├── router/                 # 路由配置
│   │   └── index.js
│   ├── store/                  # Vuex状态管理
│   │   └── index.js
│   ├── services/               # API服务调用
│   │   ├── authService.js
│   │   └── recipeService.js
│   ├── App.vue                 # 根组件
│   └── main.js                 # 入口文件
├── .env.development            # 开发环境变量配置
├── .env.production             # 生产环境变量配置
├── babel.config.js             # Babel配置
├── vue.config.js               # Vue CLI配置文件
└── package.json                # 项目依赖和脚本
```

**关键模块结构图 (简化)**

```mermaid
graph TD
    A[main.js] --> B(App.vue);
    B --> C{Vue Router};
    C --> D[views/Home.vue];
    C --> E[views/Login.vue];
    C --> F[views/RecipeDetail.vue];
    B --> G{Vuex Store};
    D --> H[components/RecipeCard.vue];
    H --> I(services/recipeService.js);
    E --> J(services/authService.js);
    I --> K[Backend API];
    J --> K;
```

### 4.2.2 后端项目结构说明

后端项目基于 Spring Boot 构建，采用经典的三层架构（Controller-Service-Repository(DAO/Mapper)），主要目录结构如下：

```
demo/ (Spring Boot项目根目录)
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/example/recipe/
│   │   │       ├── controller/         # 控制器层：处理HTTP请求，调用服务层
│   │   │       │   ├── AuthController.java
│   │   │       │   └── RecipeController.java
│   │   │       ├── service/            # 服务层：实现业务逻辑
│   │   │       │   ├── impl/           # 服务实现类
│   │   │       │   │   ├── AuthServiceImpl.java
│   │   │       │   │   └── RecipeServiceImpl.java
│   │   │       │   ├── AuthService.java
│   │   │       │   └── RecipeService.java
│   │   │       ├── repository/         # 数据访问层：与数据库交互 (JPA)
│   │   │       │   ├── UserRepository.java
│   │   │       │   └── RecipeRepository.java
│   │   │       ├── model/              # 实体类/领域模型
│   │   │       │   ├── User.java
│   │   │       │   └── Recipe.java
│   │   │       ├── dto/                # 数据传输对象
│   │   │       │   ├── UserDTO.java
│   │   │       │   └── RecipeDTO.java
│   │   │       ├── config/             # 配置类，如安全配置、Web配置
│   │   │       │   └── SecurityConfig.java
│   │   │       ├── security/           # 安全相关，如JWT工具类
│   │   │       │   └── JwtTokenProvider.java
│   │   │       ├── exception/          # 自定义异常类
│   │   │       │   └── ResourceNotFoundException.java
│   │   │       └── RecipeApplication.java # Spring Boot启动类
│   │   └── resources/
│   │       ├── application.properties  # 应用配置文件
│   │       ├── static/               # 静态资源
│   │       └── templates/            # 模板文件 (如果使用服务端渲染)
│   └── test/                     # 测试代码
└── pom.xml                       # Maven项目配置文件
```

**后端分层结构图 (简化类图)**

```mermaid
classDiagram
    class AuthController {
        +register(UserDTO)
        +login(LoginRequest)
    }
    class RecipeController {
        +getAllRecipes()
        +getRecipeById(Long)
        +createRecipe(RecipeDTO)
    }
    class AuthService {
        <<Interface>>
        +register(UserDTO)
        +login(LoginRequest)
    }
    class RecipeService {
        <<Interface>>
        +findAll()
        +findById(Long)
        +save(RecipeDTO)
    }
    class AuthServiceImpl {
        -UserRepository userRepository
        -PasswordEncoder passwordEncoder
        -JwtTokenProvider jwtTokenProvider
        +register(UserDTO)
        +login(LoginRequest)
    }
    class RecipeServiceImpl {
        -RecipeRepository recipeRepository
        +findAll()
        +findById(Long)
        +save(RecipeDTO)
    }
    class UserRepository {
        <<Interface>>
        +findByUsername(String)
        +existsByUsername(String)
        +existsByEmail(String)
    }
    class RecipeRepository {
        <<Interface>>
        +findAll()
        +findById(Long)
    }
    class User
    class Recipe

    AuthController --> AuthService
    RecipeController --> RecipeService
    AuthService <|.. AuthServiceImpl
    RecipeService <|.. RecipeServiceImpl
    AuthServiceImpl --> UserRepository
    AuthServiceImpl --> JwtTokenProvider
    RecipeServiceImpl --> RecipeRepository
    UserRepository ..> User : uses
    RecipeRepository ..> Recipe : uses
```

## 4.3 模块功能详细设计

### 4.3.1 前端核心功能实现

#### 1. 用户注册与登录流程

**流程图：**

```mermaid
graph TD
    A[用户进入注册/登录页面] --> B{输入用户名/邮箱/密码};
    B -- 注册 --> C[前端校验表单数据];
    C -- 校验通过 --> D[调用注册API];
    D -- 注册成功 --> E[提示注册成功，跳转登录页];
    D -- 注册失败 --> F[提示错误信息];
    B -- 登录 --> G[前端校验表单数据];
    G -- 校验通过 --> H[调用登录API];
    H -- 登录成功 --> I[保存Token到LocalStorage/Vuex, 跳转首页];
    H -- 登录失败 --> J[提示错误信息];
    C -- 校验失败 --> K[显示校验错误提示];
    G -- 校验失败 --> K;
```

**逻辑说明：**

*   **注册**：用户填写用户名、邮箱、密码。前端进行基本格式校验（如邮箱格式、密码长度）。校验通过后，调用后端注册接口。根据后端返回结果，提示用户注册成功或失败信息。
*   **登录**：用户填写用户名/邮箱、密码。前端进行基本格式校验。校验通过后，调用后端登录接口。登录成功后，后端返回JWT Token，前端将其存储在LocalStorage或Vuex中，并通常会重定向到用户首页或之前的页面。登录失败则提示错误。

#### 2. 菜谱列表展示与分页

**流程图：**

```mermaid
graph TD
    A[用户进入菜谱列表页] --> B{Vuex中是否有缓存数据？};
    B -- 是 --> C[从Vuex获取菜谱列表];
    B -- 否 --> D[调用获取菜谱列表API (带分页参数)];
    D -- API请求成功 --> E[更新Vuex状态，存储菜谱列表和分页信息];
    C --> F[渲染菜谱列表到页面];
    E --> F;
    D -- API请求失败 --> G[显示错误提示];
    F --> H{用户点击分页控件/加载更多};
    H --> I[更新分页参数];
    I --> D;
```

**逻辑说明：**

*   页面加载时，首先检查Vuex中是否已有菜谱数据缓存。若有，则直接使用；若无，则调用后端API获取第一页数据。
*   API请求会携带当前页码和每页数量等分页参数。
*   获取到数据后，更新到Vuex中，并渲染到页面上。同时更新分页控件的状态（总页数、当前页等）。
*   用户点击分页控件或触发加载更多时，更新分页参数，重新调用API获取对应页码的数据，并更新视图。

#### 3. 权限拦截 (路由守卫)

**逻辑说明：**

通过Vue Router的导航守卫 (`beforeEach`) 实现权限控制。

*   **定义路由元信息**：在路由配置中，为需要登录才能访问的页面添加 `meta: { requiresAuth: true }`。
*   **全局前置守卫**：
    *   在每次路由跳转前触发。
    *   检查目标路由 (`to`) 是否需要认证 (`to.matched.some(record => record.meta.requiresAuth)`)。
    *   如果需要认证，检查用户是否已登录（例如，通过检查Vuex中或LocalStorage中是否存在有效的Token）。
    *   如果需要认证但用户未登录，则重定向到登录页面 (`next({ name: 'Login' })`)，并可能保存用户想访问的原始路径，以便登录后跳转回去。
    *   如果不需要认证或用户已登录，则允许导航 (`next()`)。
    *   对于已登录用户访问登录页的情况，可以将其重定向到首页。

```javascript
// router/index.js 示例
router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters.isAuthenticated; // 从Vuex获取登录状态
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);

  if (requiresAuth && !isAuthenticated) {
    next({ name: 'Login', query: { redirect: to.fullPath } });
  } else if (to.name === 'Login' && isAuthenticated) {
    next({ name: 'Home' });
  } else {
    next();
  }
});
```

### 4.3.2 后端核心功能实现

#### 1. 用户注册与登录验证流程 (伪代码/时序图)

**时序图 (用户注册):**

```mermaid
sequenceDiagram
    participant C as Client
    participant AuthCtrl as AuthController
    participant AuthSvc as AuthService
    participant UserRepo as UserRepository
    participant PwdEncoder as PasswordEncoder

    C->>AuthCtrl: POST /api/auth/register (UserDTO)
    AuthCtrl->>AuthSvc: register(userDTO)
    AuthSvc->>UserRepo: existsByUsername(userDTO.username)?
    UserRepo-->>AuthSvc: false
    AuthSvc->>UserRepo: existsByEmail(userDTO.email)?
    UserRepo-->>AuthSvc: false
    AuthSvc->>PwdEncoder: encode(userDTO.password)
    PwdEncoder-->>AuthSvc: hashedPassword
    AuthSvc->>UserRepo: save(User entity with hashedPassword)
    UserRepo-->>AuthSvc: savedUser
    AuthSvc-->>AuthCtrl: User registered successfully
    AuthCtrl-->>C: HTTP 201 Created (Success Message)
```

**时序图 (用户登录):**

```mermaid
sequenceDiagram
    participant C as Client
    participant AuthCtrl as AuthController
    participant AuthSvc as AuthService
    participant UserRepo as UserRepository
    participant PwdEncoder as PasswordEncoder
    participant JwtProvider as JwtTokenProvider

    C->>AuthCtrl: POST /api/auth/login (LoginRequest)
    AuthCtrl->>AuthSvc: login(loginRequest)
    AuthSvc->>UserRepo: findByUsername(loginRequest.username)
    UserRepo-->>AuthSvc: userEntity (or null)
    alt User not found
        AuthSvc-->>AuthCtrl: AuthenticationException("User not found")
        AuthCtrl-->>C: HTTP 401 Unauthorized
    else User found
        AuthSvc->>PwdEncoder: matches(loginRequest.password, userEntity.password)?
        PwdEncoder-->>AuthSvc: true (or false)
        alt Password mismatch
            AuthSvc-->>AuthCtrl: AuthenticationException("Invalid credentials")
            AuthCtrl-->>C: HTTP 401 Unauthorized
        else Password matches
            AuthSvc->>JwtProvider: generateToken(userEntity.username)
            JwtProvider-->>AuthSvc: jwtToken
            AuthSvc-->>AuthCtrl: JwtResponse(token)
            AuthCtrl-->>C: HTTP 200 OK (JwtResponse)
        end
    end
```

**伪代码 (AuthService - register):**

```java
// AuthService.register(userDto)
function register(userDto):
  if userRepository.existsByUsername(userDto.username):
    throw UsernameAlreadyExistsException
  if userRepository.existsByEmail(userDto.email):
    throw EmailAlreadyExistsException

  hashedPassword = passwordEncoder.encode(userDto.password)
  User newUser = new User(userDto.username, hashedPassword, userDto.email)
  userRepository.save(newUser)
  return "User registered successfully"
```

**伪代码 (AuthService - login):**

```java
// AuthService.login(loginRequest)
function login(loginRequest):
  User user = userRepository.findByUsername(loginRequest.username)
  if user is null:
    throw BadCredentialsException

  if not passwordEncoder.matches(loginRequest.password, user.getPassword()):
    throw BadCredentialsException

  String token = jwtTokenProvider.generateToken(user.getUsername())
  return new JwtResponse(token)
```

#### 2. 菜谱数据处理逻辑 (创建菜谱 - 伪代码)

**伪代码 (RecipeService - createRecipe):**

```java
// RecipeService.createRecipe(recipeDto, currentUsername)
function createRecipe(recipeDto, currentUsername):
  User author = userRepository.findByUsername(currentUsername)
  if author is null:
    throw UserNotFoundException // Should not happen if token is valid

  Recipe newRecipe = new Recipe();
  newRecipe.setTitle(recipeDto.title);
  newRecipe.setDescription(recipeDto.description);
  newRecipe.setIngredients(recipeDto.ingredients); // Assuming ingredients are stored as JSON string or similar
  newRecipe.setSteps(recipeDto.steps); // Assuming steps are stored as JSON string or similar
  newRecipe.setImageUrl(recipeDto.imageUrl);
  newRecipe.setCookingTime(recipeDto.cookingTime);
  newRecipe.setDifficulty(recipeDto.difficulty);
  newRecipe.setAuthor(author); // Link to the user who created it
  // setCreatedAt, setUpdatedAt automatically by JPA @PrePersist/@PreUpdate or manually

  savedRecipe = recipeRepository.save(newRecipe);
  return convertToDto(savedRecipe); // Convert entity to DTO for response
```

### 4.3.3 接口协议说明 (推荐集中展示)

本系统API遵循RESTful设计原则，使用JSON格式进行数据交换，并通过HTTP状态码表示操作结果。所有需要认证的接口均需在请求头中携带 `Authorization: Bearer <JWT_TOKEN>`。

#### 1. 用户认证接口

*   **注册用户**
    *   **URL**: `POST /api/auth/register`
    *   **请求体 (application/json)**:
        ```json
        {
          "username": "testuser",
          "email": "<EMAIL>",
          "password": "password123"
        }
        ```
    *   **成功响应 (201 Created)**:
        ```json
        {
          "message": "User registered successfully!"
        }
        ```
    *   **失败响应 (400 Bad Request / 409 Conflict)**: 包含错误信息。
*   **用户登录**
    *   **URL**: `POST /api/auth/login`
    *   **请求体 (application/json)**:
        ```json
        {
          "username": "testuser",
          "password": "password123"
        }
        ```
    *   **成功响应 (200 OK)**:
        ```json
        {
          "token": "eyJhbGciOiJIUzUxMiJ9...",
          "type": "Bearer",
          "username": "testuser"
        }
        ```
    *   **失败响应 (401 Unauthorized)**: 包含错误信息。

#### 2. 菜谱管理接口

*   **获取所有菜谱 (可分页)**
    *   **URL**: `GET /api/recipes`
    *   **Query参数**: `page` (int, default 0), `size` (int, default 10), `sortBy` (string, default 'createdAt'), `sortDir` (string, default 'desc')
    *   **成功响应 (200 OK)**:
        ```json
        {
          "content": [
            { "id": 1, "title": "Spaghetti Carbonara", ... },
            { "id": 2, "title": "Chicken Curry", ... }
          ],
          "pageNo": 0,
          "pageSize": 10,
          "totalElements": 100,
          "totalPages": 10,
          "last": false
        }
        ```
*   **创建新菜谱 (需认证)**
    *   **URL**: `POST /api/recipes`
    *   **请求体 (application/json)**:
        ```json
        {
          "title": "New Recipe Title",
          "description": "Delicious recipe description.",
          "ingredients": "Ingredient 1, Ingredient 2",
          "steps": "Step 1. Do this. Step 2. Do that.",
          "imageUrl": "http://example.com/image.jpg",
          "cookingTime": 30, // minutes
          "difficulty": "Easy"
        }
        ```
    *   **成功响应 (201 Created)**: 返回创建的菜谱对象。
        ```json
        {
          "id": 3,
          "title": "New Recipe Title",
          ...
        }
        ```
*   **获取单个菜谱详情**
    *   **URL**: `GET /api/recipes/{id}`
    *   **成功响应 (200 OK)**: 返回指定ID的菜谱对象。
    *   **失败响应 (404 Not Found)**: 如果菜谱不存在。
*   **更新菜谱 (需认证，且为菜谱作者)**
    *   **URL**: `PUT /api/recipes/{id}`
    *   **请求体**: 同创建菜谱。
    *   **成功响应 (200 OK)**: 返回更新后的菜谱对象。
    *   **失败响应 (403 Forbidden / 404 Not Found)**。
*   **删除菜谱 (需认证，且为菜谱作者或管理员)**
    *   **URL**: `DELETE /api/recipes/{id}`
    *   **成功响应 (200 OK)**:
        ```json
        {
          "message": "Recipe deleted successfully!"
        }
        ```
    *   **失败响应 (403 Forbidden / 404 Not Found)**。

#### 3. 收藏管理接口 (需认证)

*   **添加收藏**
    *   **URL**: `POST /api/favorites/{recipeId}`
    *   **成功响应 (201 Created)**:
        ```json
        {
          "message": "Recipe favorited successfully!"
        }
        ```
    *   **失败响应 (404 Not Found - recipe not found / 409 Conflict - already favorited)**
*   **移除收藏**
    *   **URL**: `DELETE /api/favorites/{recipeId}`
    *   **成功响应 (200 OK)**:
        ```json
        {
          "message": "Recipe unfavorited successfully!"
        }
        ```
    *   **失败响应 (404 Not Found - favorite entry not found)**
*   **获取当前用户收藏列表 (可分页)**
    *   **URL**: `GET /api/favorites`
    *   **Query参数**: `page` (int, default 0), `size` (int, default 10)
    *   **成功响应 (200 OK)**: 返回菜谱列表分页对象 (同获取所有菜谱接口)。

## 4.4 关键模块程序设计与伪代码

本节将对系统中几个关键模块的核心逻辑以程序流程图、框图或伪代码的形式进行具体展开。

### 4.4.1 用户登录模块

用户登录模块的核心逻辑已在 `4.3.1 前端核心功能实现` (登录流程图) 和 `4.3.2 后端核心功能实现` (用户登录验证流程时序图和伪代码) 中详细描述。主要涉及前端表单校验、API调用、后端身份验证（用户名密码校验、Token生成与返回）以及前端Token存储和路由跳转。

**后端登录核心伪代码 (简化 - Controller & Service):**

```java
// AuthController.java
@PostMapping("/login")
public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
    // ... (调用AuthService.login)
    // ... (返回JWTResponse)
}

// AuthService.java (部分已在4.3.2展示)
public JwtResponse login(LoginRequest loginRequest) {
    Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword()));
    SecurityContextHolder.getContext().setAuthentication(authentication);
    String jwt = jwtTokenProvider.generateToken(authentication);
    UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
    return new JwtResponse(jwt, userDetails.getUsername());
}
```

### 4.4.2 菜谱管理模块 (创建与编辑)

菜谱管理模块允许用户创建、查看、编辑和删除自己的菜谱。管理员可以管理所有菜谱。

**创建/编辑菜谱流程图 (后端视角):**

```mermaid
graph TD
    A[Controller接收创建/更新菜谱请求 DTO] --> B{校验请求数据 (如标题非空)};
    B -- 校验失败 --> C[返回400 Bad Request];
    B -- 校验通过 --> D{获取当前认证用户信息};
    D -- 获取失败 (理论上不应发生) --> E[返回401 Unauthorized];
    D -- 获取成功 --> F{操作类型是更新?};
    F -- 是 (更新) --> G[Service: 查找待更新菜谱 by ID];
    G -- 菜谱不存在 --> H[返回404 Not Found];
    G -- 菜谱存在 --> I{当前用户是菜谱作者或管理员?};
    I -- 否 --> J[返回403 Forbidden];
    I -- 是 --> K[Service: 更新菜谱数据];
    F -- 否 (创建) --> L[Service: 创建新菜谱实体];
    L --> M[Service: 保存菜谱到数据库];
    K --> M;
    M -- 保存成功 --> N[返回201 (创建) 或 200 (更新) 及菜谱DTO];
    M -- 保存失败 --> O[返回500 Internal Server Error];
```

**伪代码 (RecipeService - updateRecipe):**

```java
// RecipeService.updateRecipe(recipeId, recipeDto, currentUsername)
function updateRecipe(recipeId, recipeDto, currentUsername):
  Recipe existingRecipe = recipeRepository.findById(recipeId)
    .orElseThrow(() -> new ResourceNotFoundException("Recipe", "id", recipeId));

  User currentUser = userRepository.findByUsername(currentUsername)
    .orElseThrow(() -> new UsernameNotFoundException("User not found"));

  // 权限检查：只有作者或管理员可以修改
  if (!existingRecipe.getAuthor().getId().equals(currentUser.getId()) && !currentUser.isAdminRole()) { // 假设有isAdminRole()
    throw new AccessDeniedException("You don't have permission to update this recipe");
  }

  existingRecipe.setTitle(recipeDto.title);
  existingRecipe.setDescription(recipeDto.description);
  existingRecipe.setIngredients(recipeDto.ingredients);
  existingRecipe.setSteps(recipeDto.steps);
  existingRecipe.setImageUrl(recipeDto.imageUrl);
  existingRecipe.setCookingTime(recipeDto.cookingTime);
  existingRecipe.setDifficulty(recipeDto.difficulty);
  // updated_at 会自动更新

  updatedRecipe = recipeRepository.save(existingRecipe);
  return convertToDto(updatedRecipe);
```

### 4.4.3 菜谱展示模块 (列表与详情)

菜谱展示模块负责向用户呈现菜谱列表和单个菜谱的详细信息。

**获取菜谱列表 (后端分页与排序) 伪代码 (RecipeService):**

```java
// RecipeService.getAllRecipes(page, size, sortBy, sortDir)
function getAllRecipes(page, size, sortBy, sortDir):
  Sort sort = sortDir.equalsIgnoreCase(Sort.Direction.ASC.name()) ? Sort.by(sortBy).ascending() :
                                                                  Sort.by(sortBy).descending();
  Pageable pageable = PageRequest.of(page, size, sort);
  Page<Recipe> recipesPage = recipeRepository.findAll(pageable);

  List<RecipeDTO> content = recipesPage.getContent().stream()
                            .map(this::convertToDto)
                            .collect(Collectors.toList());

  return new PagedResponse<>(content, recipesPage.getNumber(), recipesPage.getSize(),
                             recipesPage.getTotalElements(), recipesPage.getTotalPages(), recipesPage.isLast());
```

**获取菜谱详情伪代码 (RecipeService):**

```java
// RecipeService.getRecipeById(recipeId)
function getRecipeById(recipeId):
  Recipe recipe = recipeRepository.findById(recipeId)
    .orElseThrow(() -> new ResourceNotFoundException("Recipe", "id", recipeId));
  return convertToDto(recipe);
```

前端通过调用这些API，并将获取到的数据显示在相应的视图组件中。列表页通常包含搜索、筛选和分页功能，详情页则展示菜谱的完整信息，如食材、步骤、图片、作者等。

# 5. 系统测试

## 5.1 测试计划与策略

为确保系统质量，将采用多层次的测试策略，包括单元测试、集成测试、API接口测试和UI自动化测试。

*   **测试目标**：验证系统各项功能是否符合需求规格，确保系统稳定、可靠、易用，并具有良好的性能和安全性。
*   **测试范围**：覆盖所有核心功能模块，包括用户管理、菜谱管理、收藏管理、搜索等。
*   **测试环境**：搭建与生产环境相似的独立测试环境。
*   **测试资源**：测试人员、测试设备、测试工具（JUnit, Mockito, Postman, Selenium/Cypress）。

## 5.2 测试用例设计

针对每个功能模块设计详细的测试用例，覆盖正常场景、异常场景和边界条件。

### 5.2.1 用户注册模块测试用例 (示例)

| 用例ID | 测试点 | 输入数据 | 预期输出 | 测试结果 |
| --- | --- | --- | --- | --- |
| TC_REG_001 | 正常注册 | 用户名: newuser, 邮箱: <EMAIL>, 密码: Pass123 | 注册成功，跳转登录页 |  |
| TC_REG_002 | 用户名已存在 | 用户名: existinguser, ... | 提示用户名已存在 |  |
| TC_REG_003 | 邮箱已存在 | 邮箱: <EMAIL>, ... | 提示邮箱已存在 |  |
| TC_REG_004 | 密码过短 | 密码: Pw1 | 提示密码长度不足 |  |
| TC_REG_005 | 邮箱格式错误 | 邮箱: test.com, ... | 提示邮箱格式无效 |  |
| ... | ... | ... | ... |  |

### 5.2.2 菜谱发布模块测试用例 (示例)

| 用例ID | 测试点 | 输入数据 | 预期输出 | 测试结果 |
| --- | --- | --- | --- | --- |
| TC_REC_ADD_001 | 正常发布菜谱 | 标题、描述、食材、步骤等均有效 | 发布成功，菜谱列表可见 |  |
| TC_REC_ADD_002 | 标题为空 | 标题: "", ... | 提示标题不能为空 |  |
| TC_REC_ADD_003 | 食材为空 | 食材: "", ... | 提示食材不能为空 |  |
| ... | ... | ... | ... |  |

## 5.3 测试执行与结果分析

按照测试计划执行测试用例，记录测试结果，对发现的缺陷进行跟踪和管理，直至缺陷修复并通过回归测试。

*   **单元测试**：使用JUnit和Mockito对后端Service层和工具类进行测试，确保代码逻辑的正确性。
*   **集成测试**：测试模块间的交互，如Controller与Service的集成，Service与Repository的集成。
*   **API接口测试**：使用Postman对所有后端API接口进行测试，验证请求参数、响应数据和HTTP状态码的正确性。
*   **UI测试**：手动测试关键用户流程，或使用Selenium/Cypress编写自动化脚本测试前端UI交互和显示。

测试完成后，将出具测试报告，总结测试情况、缺陷统计和系统质量评估。

# 6. 总结与展望

## 6.1 项目总结

本项目设计并实现了一个基于Vue.js和Spring Boot的在线菜谱分享与管理系统。系统主要包括用户管理、菜谱浏览、菜谱发布、菜谱搜索、菜谱收藏等核心功能。前端采用Vue.js全家桶技术，实现了动态交互和组件化开发；后端采用Spring Boot框架，结合Spring Security进行安全控制，Spring Data JPA进行数据持久化，并设计了RESTful API与前端进行数据交互。数据库选用MySQL存储用户信息、菜谱数据及收藏关系。

在开发过程中，遵循了模块化设计原则，对系统进行了合理的分层，提高了代码的可维护性和可扩展性。通过详细的需求分析、系统设计、数据库设计和接口设计，确保了项目的顺利进行。系统测试阶段通过多层次的测试保证了软件的质量和稳定性。

## 6.2 系统亮点与不足

**亮点：**

*   **技术栈先进**：采用了业界主流的前后端分离技术栈，具有良好的开发效率和运行性能。
*   **用户体验良好**：前端界面简洁美观，交互流畅，注重用户操作的便捷性。
*   **功能完善**：基本覆盖了在线菜谱平台的核心功能需求。
*   **安全性考虑**：后端通过Spring Security和JWT实现了用户认证和授权，保障了系统安全。

**不足与待改进：**

*   **高级功能缺失**：如用户评论与评分、个性化推荐、食材购买链接等高级功能尚未实现。
*   **性能优化空间**：对于大量数据和高并发场景，可能还需要进一步的性能调优，如引入更复杂的缓存策略、数据库读写分离等。
*   **移动端适配**：目前主要针对Web端设计，移动端适配可以进一步优化或开发原生App。
*   **自动化测试覆盖率**：虽然规划了测试，但实际项目中自动化测试的覆盖率可能需要持续提升。

## 6.3 未来展望

未来可以从以下几个方面对系统进行扩展和优化：

1.  **丰富社交功能**：增加用户间的关注、私信、菜谱评论互动、评分系统等，增强社区属性。
2.  **智能化推荐**：基于用户行为数据（浏览、收藏、搜索历史）和协同过滤算法，实现个性化菜谱推荐。
3.  **食材管理与购物清单**：允许用户根据菜谱生成购物清单，甚至对接电商平台提供一键购买食材的功能。
4.  **多媒体支持**：支持用户上传烹饪视频，丰富菜谱展示形式。
5.  **国际化与本地化**：支持多语言，满足不同地区用户需求。
6.  **持续集成与部署优化**：完善CI/CD流程，提高部署效率和系统稳定性。
7.  **微服务架构演进**：当系统规模和用户量进一步增长时，可以考虑将部分模块拆分为微服务，以提升系统的可伸缩性和容错性。

通过不断的迭代和优化，期望本菜谱分享系统能够成为一个功能更强大、用户体验更佳的在线美食平台。