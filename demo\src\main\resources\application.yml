# Spring Boot 配置文件
spring:
  # 数据源配置
  datasource:
    url: ****************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: gwh842
      
  # JPA 配置
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        
  # Jackson 配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  web:
    resources:
      static-locations: classpath:/static/
    
# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
    
# 日志配置
logging:
  level:
    com.recipe: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    
# JWT 配置
jwt:
  secret: mySecretKey123456789012345678901234567890
  
# 七牛云配置
qiniu:
  accessKey: k1nZF6J_iEho2pzKCL1llgTNVuKVCR_uimGHGiL6
  secretKey: pRXFJ5cqZxqcyijGrNzNHuycpqWyWER4kZZrKLQZ
  bucket: gds-gwh
  domain: sy0haap8o.hn-bkt.clouddn.com
  expiration: 86400000  # 24小时（毫秒）
  
# 上传路径配置
upload:
  image:
    path: uploads/images/
    
# 应用配置
app:
  name: Recipe Management System
  version: 1.0.0
  description: 菜谱管理系统后端API