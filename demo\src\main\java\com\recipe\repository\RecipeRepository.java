package com.recipe.repository;

import com.recipe.entity.Recipe;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RecipeRepository extends JpaRepository<Recipe, Long> {
    
    /**
     * 根据菜名搜索菜谱（模糊查询）
     */
    Page<Recipe> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * 根据关键词搜索菜谱（在菜名、描述、食材中搜索）
     */
    @Query("SELECT r FROM Recipe r WHERE " +
           "LOWER(r.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(r.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(r.ingredients) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<Recipe> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 获取最新的菜谱
     */
    Page<Recipe> findAllByOrderByCreateTimeDesc(Pageable pageable);
}