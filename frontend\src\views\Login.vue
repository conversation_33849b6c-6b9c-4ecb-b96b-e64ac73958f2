<template>
  <div class="login-container">
    <div class="login-form">
      <div class="form-header">
        <h2>用户登录</h2>
        <p>欢迎回到菜谱管理系统</p>
      </div>
      
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        label-width="0px"
        class="login-form-content"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="el-icon-user"
            size="large"
            clearable
          ></el-input>
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="el-icon-lock"
            size="large"
            show-password
            @keyup.enter.native="handleLogin"
          ></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-btn"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
        
        <div class="form-footer">
          <span>还没有账号？</span>
          <el-button type="text" @click="$router.push('/register')">
            立即注册
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      loading: false,
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ]
      }
    }
  },
  
  methods: {
    async handleLogin() {
      try {
        // 表单验证
        await this.$refs.loginForm.validate()
        
        this.loading = true
        
        // 调用登录接口
        const result = await this.$store.dispatch('auth/login', this.loginForm)
        console.log('登录结果:', result)
        
        this.$message.success('登录成功！')
        
        // 跳转到首页或之前访问的页面
        const redirect = this.$route.query.redirect || '/'
        this.$router.push(redirect)
        
        // 强制刷新页面以确保状态更新
        setTimeout(() => {
          window.location.reload()
        }, 300)
        
      } catch (error) {
        console.error('登录失败:', error)
        
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(error.response.data.message)
        } else {
          this.$message.error('登录失败，请检查用户名和密码')
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-form {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.form-header {
  text-align: center;
  padding: 40px 30px 20px;
  background: linear-gradient(135deg, #409EFF 0%, #36a3f7 100%);
  color: white;
}

.form-header h2 {
  margin: 0 0 10px;
  font-size: 28px;
  font-weight: 300;
}

.form-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.login-form-content {
  padding: 30px;
}

.login-btn {
  width: 100%;
  height: 45px;
  font-size: 16px;
  border-radius: 6px;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
  color: #666;
  font-size: 14px;
}

.form-footer .el-button--text {
  color: #409EFF;
  font-weight: 500;
  padding: 0;
  margin-left: 5px;
}

.form-footer .el-button--text:hover {
  color: #66b1ff;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-form {
    margin: 0;
  }
  
  .form-header {
    padding: 30px 20px 15px;
  }
  
  .form-header h2 {
    font-size: 24px;
  }
  
  .login-form-content {
    padding: 20px;
  }
}
</style>