# 菜谱管理系统 - 安装部署手册

## 📋 系统概述

本项目是一个基于 Spring Boot + Vue.js 的菜谱管理系统，包含用户认证、菜谱管理、收藏等功能。

### 技术栈
- **后端**: Spring Boot 2.x + MySQL + JWT
- **前端**: Vue.js 2.x + Element UI + Vuex
- **构建工具**: Maven (后端) + npm/yarn (前端)

---

## 🛠️ 环境要求

### 基础环境
- **Java**: JDK 8 或更高版本
- **Node.js**: 14.x 或更高版本
- **MySQL**: 5.7 或更高版本
- **Maven**: 3.6 或更高版本

### 开发工具推荐
- **IDE**: IntelliJ IDEA / Eclipse (后端)
- **编辑器**: VS Code / WebStorm (前端)
- **数据库工具**: Navicat / MySQL Workbench

---

## 📦 后端安装部署

### 1. 环境检查
```bash
# 检查 Java 版本
java -version

# 检查 Maven 版本
mvn -version

# 检查 MySQL 服务状态
# Windows:
net start mysql
# 或者通过服务管理器检查 MySQL 服务
```

### 2. 数据库配置
```sql
-- 创建数据库
CREATE DATABASE recipe_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选，建议生产环境使用）
CREATE USER 'recipe_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON recipe_db.* TO 'recipe_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 后端项目配置
```bash
# 进入后端项目目录
cd f:\recipe\demo

# 复制配置文件模板（如果存在）
# cp src/main/resources/application.yml.example src/main/resources/application.yml
```

### 4. 修改数据库配置
编辑 `src/main/resources/application.yml` 或 `application.properties`：

```yaml
# application.yml 示例
spring:
  datasource:
    url: ****************************************************************************************************************
    username: recipe_user  # 或者 root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: update  # 首次运行使用 create，后续使用 update
    show-sql: true
    database-platform: org.hibernate.dialect.MySQL8Dialect

# JWT 配置
jwt:
  secret: your-secret-key-here  # 建议使用复杂的密钥
  expiration: 86400  # 24小时，单位：秒

# 文件上传配置
file:
  upload:
    path: ./uploads/  # 文件上传路径
```

### 5. 安装依赖并启动
```bash
# 清理并安装依赖
mvn clean install

# 跳过测试安装（如果测试有问题）
mvn clean install -DskipTests

# 启动应用
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/demo-0.0.1-SNAPSHOT.jar
```

### 6. 验证后端启动
```bash
# 检查应用是否启动成功
curl http://localhost:8080/api/users/validate-token

# 或者在浏览器访问
# http://localhost:8080/api/users/validate-token
```

---

## 🎨 前端安装部署

### 1. 环境检查
```bash
# 检查 Node.js 版本
node --version

# 检查 npm 版本
npm --version

# 如果使用 yarn
yarn --version
```

### 2. 安装依赖
```bash
# 进入前端项目目录
cd f:\recipe\frontend

# 使用 npm 安装依赖
npm install

# 或者使用 yarn（推荐，速度更快）
yarn install

# 如果安装速度慢，可以使用国内镜像
npm install --registry=https://registry.npm.taobao.org
```

### 3. 环境配置
创建环境配置文件（如果不存在）：

```bash
# 开发环境配置
echo "VUE_APP_API_BASE_URL=http://localhost:8080" > .env.development

# 生产环境配置
echo "VUE_APP_API_BASE_URL=http://your-production-domain.com" > .env.production
```

### 4. 启动开发服务器
```bash
# 启动开发服务器
npm run serve

# 或者使用 yarn
yarn serve

# 指定端口启动（可选）
npm run serve -- --port 8081
```

### 5. 构建生产版本
```bash
# 构建生产版本
npm run build

# 或者使用 yarn
yarn build

# 构建完成后，dist 目录包含所有静态文件
```

### 6. 验证前端启动
```bash
# 开发服务器通常在以下地址启动：
# http://localhost:8080 或 http://localhost:8081

# 检查网络连接
curl http://localhost:8081
```

---

## 🚀 完整部署流程

### 开发环境快速启动
```bash
# 1. 启动数据库服务
net start mysql

# 2. 启动后端服务
cd f:\recipe\demo
mvn spring-boot:run

# 3. 新开终端，启动前端服务
cd f:\recipe\frontend
npm run serve

# 4. 访问应用
# 前端: http://localhost:8081
# 后端API: http://localhost:8080
```

### 生产环境部署
```bash
# 1. 后端打包
cd f:\recipe\demo
mvn clean package -DskipTests

# 2. 前端构建
cd f:\recipe\frontend
npm run build

# 3. 部署后端（示例）
# 将 target/demo-0.0.1-SNAPSHOT.jar 上传到服务器
# java -jar demo-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod

# 4. 部署前端（示例）
# 将 dist/ 目录内容上传到 Web 服务器（如 Nginx）
```

---

## 🔧 常见问题解决

### 后端问题

#### 1. 数据库连接失败
```bash
# 检查 MySQL 服务状态
net start mysql

# 检查端口占用
netstat -an | findstr 3306

# 测试数据库连接
mysql -u root -p -h localhost -P 3306
```

#### 2. 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr 8080

# 杀死占用进程（替换 PID）
taskkill /PID <进程ID> /F

# 或者修改配置文件中的端口
# server.port=8081
```

#### 3. Maven 依赖下载失败
```bash
# 清理本地仓库
mvn dependency:purge-local-repository

# 使用阿里云镜像（修改 ~/.m2/settings.xml）
# 或者删除 ~/.m2/repository 重新下载
```

### 前端问题

#### 1. npm 安装依赖失败
```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules 重新安装
rmdir /s node_modules
npm install

# 使用 yarn 替代 npm
npm install -g yarn
yarn install
```

#### 2. 跨域问题
检查 `vue.config.js` 配置：
```javascript
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
}
```

#### 3. 编译错误
```bash
# 检查 Node.js 版本兼容性
node --version

# 更新依赖到最新版本
npm update

# 如果是 ESLint 错误，可以临时禁用
# 在 .eslintrc.js 中添加规则或使用 --fix
npm run serve -- --fix
```

---

## 📝 开发注意事项

### 1. 代码规范
- 后端遵循 Java 编码规范
- 前端使用 ESLint + Prettier
- 提交前运行测试和代码检查

### 2. 数据库迁移
- 生产环境建议手动执行 SQL 脚本
- 开发环境可以使用 `ddl-auto: update`
- 重要数据变更前务必备份

### 3. 安全配置
- 生产环境修改默认密码
- 使用强密码和复杂的 JWT 密钥
- 配置 HTTPS 和防火墙规则

### 4. 性能优化
- 前端启用 gzip 压缩
- 后端配置连接池
- 数据库添加适当索引

---

## 📞 技术支持

如果在安装部署过程中遇到问题，请检查：

1. **日志文件**: 查看控制台输出和日志文件
2. **网络连接**: 确保端口未被占用，防火墙配置正确
3. **版本兼容**: 确认各组件版本兼容性
4. **配置文件**: 检查数据库连接、API 地址等配置

---

*最后更新时间: 2024年12月*
*版本: v1.0*