<template>
  <div class="recipe-list">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="page-header">
          <h1>菜谱列表</h1>
          <el-input
            placeholder="搜索菜谱"
            v-model="searchQuery"
            class="search-input"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
            @clear="handleSearch">
          </el-input>
        </div>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" v-loading="loading">
      <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="recipe in filteredRecipes" :key="recipe.id" class="recipe-card-col">
        <el-card :body-style="{ padding: '0px' }" shadow="hover" class="recipe-card">
          <div class="recipe-image" :style="{ backgroundImage: `url(${getImageUrl(recipe.imageUrl)})` }"></div>
          <div class="recipe-content">
            <h3 class="recipe-title">{{ recipe.name }}</h3>
            <p class="recipe-desc">{{ recipe.description | truncate }}</p>
            <div class="recipe-footer">
              <el-button type="text" @click="viewRecipeDetail(recipe.id)">查看详情</el-button>
              <div class="recipe-actions">
                <el-tooltip content="收藏" placement="top" v-if="isLoggedIn">
                  <i 
                    :class="['el-icon-star-' + (isFavorite(recipe.id) ? 'on' : 'off'), 'favorite-icon']"
                    @click="toggleFavorite(recipe.id)"></i>
                </el-tooltip>
                <span class="recipe-time">{{ recipe.createTime | formatDate }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <div class="empty-state" v-if="filteredRecipes.length === 0 && !loading">
      <el-empty description="暂无菜谱"></el-empty>
    </div>
    
    <!-- 添加分页组件 -->
    <div class="pagination-wrapper" v-if="pagination.totalElements > 0">
      <el-pagination
        @current-change="handlePageChange"
        :current-page="pagination.number + 1"
        :page-size="pagination.size"
        :total="pagination.totalElements"
        layout="total, prev, pager, next, jumper">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { resolveImageUrl } from '@/utils/imageUtils'

export default {
  name: 'RecipeList',
  data() {
    return {
      loading: false,
      searchQuery: '',
      currentPage: 1
    }
  },
  computed: {
    ...mapGetters('auth', ['isLoggedIn']),
    ...mapGetters('favorite', ['isFavorite']),
    ...mapGetters('recipe', ['allRecipes', 'pagination']),
    filteredRecipes() {
      console.log('🔍 [RecipeList] filteredRecipes computed 被调用')
      console.log('📊 [RecipeList] this.allRecipes:', this.allRecipes)
      console.log('📊 [RecipeList] allRecipes 数量:', this.allRecipes ? this.allRecipes.length : 0)
      console.log('🔍 [RecipeList] searchQuery:', this.searchQuery)
      
      if (!this.searchQuery) {
        const result = this.allRecipes || []
        console.log('✅ [RecipeList] 返回所有菜谱，数量:', result.length)
        return result
      }
      
      const query = this.searchQuery.toLowerCase()
      const filtered = (this.allRecipes || []).filter(recipe => {
        const name = (recipe.name || '').toLowerCase()
        const description = (recipe.description || '').toLowerCase()
        const ingredients = (recipe.ingredients || '').toLowerCase()
        return name.includes(query) || 
               description.includes(query) ||
               ingredients.includes(query)
      })
      console.log('✅ [RecipeList] 过滤后的菜谱数量:', filtered.length)
      return filtered
    }
  },
  filters: {
    truncate(value) {
      if (!value) return ''
      if (value.length > 50) {
        return value.substring(0, 50) + '...'
      }
      return value
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  },
  created() {
    this.fetchRecipes()
    if (this.isLoggedIn) {
      this.fetchFavorites()
    }
  },
  methods: {
    async fetchRecipes(page = 0) {
      console.log('🚀 [RecipeList] fetchRecipes 开始调用，页码:', page)
      this.loading = true
      try {
        console.log('📡 [RecipeList] 调用 store.dispatch fetchRecipes')
        await this.$store.dispatch('recipe/fetchRecipes', { page, size: 10 })
        console.log('✅ [RecipeList] store.dispatch 调用完成')
        console.log('📊 [RecipeList] 调用完成后的 allRecipes:', this.$store.getters['recipe/allRecipes'])
      } catch (error) {
        console.error('❌ [RecipeList] 获取菜谱列表失败:', error)
        this.$message.error('获取菜谱列表失败')
      } finally {
        this.loading = false
        console.log('🏁 [RecipeList] fetchRecipes 调用结束')
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchRecipes(page - 1) // 后端页码从0开始
    },
    async fetchFavorites() {
      try {
        console.log('获取收藏ID列表')
        await this.$store.dispatch('favorite/fetchFavoriteIds')
      } catch (error) {
        console.error('获取收藏列表失败', error)
      }
    },
    viewRecipeDetail(id) {
      this.$router.push(`/recipe/detail/${id}`)
    },
    async toggleFavorite(recipeId) {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      
      try {
        console.log('切换收藏状态:', recipeId, this.isFavorite(recipeId))
        if (this.isFavorite(recipeId)) {
          await this.$store.dispatch('favorite/removeFromFavorites', recipeId)
          this.$message.success('已取消收藏')
        } else {
          await this.$store.dispatch('favorite/addToFavorites', recipeId)
          this.$message.success('收藏成功')
        }
        // 重新获取收藏ID列表
        await this.$store.dispatch('favorite/fetchFavoriteIds')
      } catch (error) {
        this.$message.error('操作失败，请稍后重试')
        console.error('收藏操作失败:', error)
      }
    },
    handleSearch() {
      // 搜索功能通过计算属性filteredRecipes实现
      // 当searchQuery变化时，filteredRecipes会自动更新
    },
    // 解析图片URL
    getImageUrl(imageUrl) {
      return resolveImageUrl(imageUrl) || 'https://via.placeholder.com/300x200?text=暂无图片'
    }
  }
}
</script>

<style scoped>
.recipe-list {
  padding: 20px 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
}

.search-input {
  width: 300px;
}

.recipe-card-col {
  margin-bottom: 20px;
}

.recipe-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s;
}

.recipe-card:hover {
  transform: translateY(-5px);
}

.recipe-image {
  height: 200px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.recipe-content {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.recipe-title {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  color: #303133;
}

.recipe-desc {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  flex: 1;
}

.recipe-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recipe-actions {
  display: flex;
  align-items: center;
}

.recipe-time {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}

.favorite-icon {
  cursor: pointer;
  font-size: 18px;
  color: #F56C6C;
}

.el-icon-star-on {
  color: #F56C6C;
}

.el-icon-star-off {
  color: #C0C4CC;
}

.empty-state {
  margin-top: 40px;
}

.pagination-wrapper {
  margin-top: 30px;
  text-align: center;
}
</style>