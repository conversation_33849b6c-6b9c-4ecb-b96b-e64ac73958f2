import request from '@/utils/request'

// 获取所有菜谱（支持分页和搜索）
export function getRecipes(params = {}) {
  return request({
    url: '/api/recipes',
    method: 'get',
    params: {
      page: params.page || 0,
      size: params.size || 10,
      keyword: params.keyword || '',
      category: params.category || '',
      ...params
    }
  })
}

// 获取菜谱详情
export function getRecipeById(id) {
  return request({
    url: `/api/recipes/${id}`,
    method: 'get'
  })
}

// 创建新菜谱
export function createRecipe(data) {
  return request({
    url: '/api/recipes',
    method: 'post',
    data
  })
}

// 更新菜谱
export function updateRecipe(id, data) {
  return request({
    url: `/api/recipes/${id}`,
    method: 'put',
    data
  })
}

// 删除菜谱
export function deleteRecipe(id) {
  return request({
    url: `/api/recipes/${id}`,
    method: 'delete'
  })
}

// 搜索菜谱
export const searchRecipes = (params = {}) => {
  return request({
    url: '/api/recipes/search',
    method: 'get',
    params: {
      name: params.name || '',
      page: params.page || 0,
      size: params.size || 10
    }
  })
}

// 获取最新菜谱
export const getLatestRecipes = (params = {}) => {
  return request({
    url: '/api/recipes/latest',
    method: 'get',
    params: {
      page: params.page || 0,
      size: params.size || 10
    }
  })
}