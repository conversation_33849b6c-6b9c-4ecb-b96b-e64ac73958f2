<template>
  <div class="recipe-detail" v-loading="loading">
    <div v-if="recipe" class="recipe-container">
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="recipe-header">
            <h1>{{ recipe.name }}</h1>
            <div class="recipe-meta">
              <span class="recipe-time">创建时间：{{ recipe.createTime | formatDate }}</span>
              <el-button 
                v-if="isLoggedIn"
                :type="isFavorite(recipe.id) ? 'danger' : 'default'"
                size="small"
                icon="el-icon-star-on"
                @click="toggleFavorite(recipe.id)">
                {{ isFavorite(recipe.id) ? '取消收藏' : '收藏' }}
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :md="12" :sm="24">
          <el-card shadow="hover" class="recipe-image-card">
            <img 
              :src="getImageUrl(recipe.imageUrl)" 
              class="recipe-image" 
              alt="菜谱图片">
          </el-card>
        </el-col>
        
        <el-col :md="12" :sm="24">
          <el-card shadow="hover" class="recipe-info-card">
            <div slot="header" class="card-header">
              <h3>菜谱介绍</h3>
            </div>
            <p class="recipe-description">{{ recipe.description }}</p>
            
            <div class="recipe-ingredients">
              <h3>食材</h3>
              <el-divider></el-divider>
              <p>{{ recipe.ingredients }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" class="recipe-steps-card">
            <div slot="header" class="card-header">
              <h3>烹饪步骤</h3>
            </div>
            <div class="recipe-steps">
              <p>{{ recipe.instructions || recipe.steps }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row>
        <el-col :span="24" class="action-buttons">
          <el-button @click="$router.push('/recipes')">返回列表</el-button>
        </el-col>
      </el-row>
    </div>
    
    <div v-else-if="!loading" class="recipe-not-found">
      <el-empty description="菜谱不存在或已被删除"></el-empty>
      <el-button @click="$router.push('/recipes')" style="margin-top: 20px;">返回列表</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { resolveImageUrl } from '@/utils/imageUtils'

export default {
  name: 'RecipeDetail',
  data() {
    return {
      loading: false
    }
  },
  computed: {
    ...mapGetters('auth', ['isLoggedIn']),
    ...mapGetters('favorite', ['favoriteIds']),
    recipe() {
      return this.$store.getters['recipe/currentRecipe']
    },
    recipeId() {
      return parseInt(this.$route.params.id)
    },
    isFavorite() {
      return (recipeId) => {
        return this.favoriteIds && this.favoriteIds.includes(parseInt(recipeId))
      }
    }
  },
  filters: {
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  },
  created() {
    this.fetchRecipeDetail()
    if (this.isLoggedIn) {
      this.fetchFavorites()
    }
  },
  beforeDestroy() {
    // 清除当前菜谱，避免缓存
    this.$store.dispatch('clearCurrentRecipe')
  },
  methods: {
    async fetchRecipeDetail() {
      this.loading = true
      try {
        await this.$store.dispatch('fetchRecipeById', this.recipeId)
      } catch (error) {
        this.$message.error('获取菜谱详情失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    async fetchFavorites() {
      try {
        console.log('获取收藏ID列表')
        await this.$store.dispatch('favorite/fetchFavoriteIds')
      } catch (error) {
        console.error('获取收藏列表失败', error)
      }
    },
    async toggleFavorite(recipeId) {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      
      try {
        console.log('切换收藏状态:', recipeId, this.isFavorite(recipeId))
        if (this.isFavorite(recipeId)) {
          await this.$store.dispatch('favorite/removeFromFavorites', recipeId)
          this.$message.success('已取消收藏')
        } else {
          await this.$store.dispatch('favorite/addToFavorites', recipeId)
          this.$message.success('收藏成功')
        }
        // 重新获取收藏ID列表
        await this.$store.dispatch('favorite/fetchFavoriteIds')
      } catch (error) {
        this.$message.error('操作失败，请稍后重试')
        console.error('收藏操作失败:', error)
      }
    },
    // 解析图片URL
    getImageUrl(imageUrl) {
      return resolveImageUrl(imageUrl) || 'https://via.placeholder.com/600x400?text=暂无图片'
    }
  }
}
</script>

<style scoped>
.recipe-detail {
  padding: 20px 0;
}

.recipe-container {
  max-width: 1200px;
  margin: 0 auto;
}

.recipe-header {
  margin-bottom: 20px;
}

.recipe-header h1 {
  margin-bottom: 10px;
  font-size: 28px;
  color: #303133;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #909399;
  font-size: 14px;
}

.recipe-image-card,
.recipe-info-card,
.recipe-steps-card {
  margin-bottom: 20px;
  height: 100%;
}

.recipe-image {
  width: 100%;
  display: block;
  border-radius: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.recipe-description {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 20px;
}

.recipe-ingredients h3,
.recipe-steps h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 500;
}

.recipe-ingredients p,
.recipe-steps p {
  line-height: 1.8;
  color: #606266;
  white-space: pre-line;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}

.recipe-not-found {
  text-align: center;
  padding: 40px 0;
}
</style>