<template>
  <div class="favorites">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="page-header">
          <h1>我的收藏</h1>
        </div>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" v-loading="loading">
      <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="favorite in favorites" :key="favorite.id" class="recipe-card-col">
        <el-card :body-style="{ padding: '0px' }" shadow="hover" class="recipe-card">
          <div class="recipe-image" :style="{ backgroundImage: `url(${favorite.recipe.imageUrl || 'https://via.placeholder.com/300x200?text=暂无图片'})` }"></div>
          <div class="recipe-content">
            <h3 class="recipe-title">{{ favorite.recipe.name }}</h3>
            <p class="recipe-desc">{{ favorite.recipe.description | truncate }}</p>
            <div class="recipe-footer">
              <el-button type="text" @click="viewRecipeDetail(favorite.recipe.id)">查看详情</el-button>
              <div class="recipe-actions">
                <el-tooltip content="取消收藏" placement="top">
                  <i class="el-icon-star-on favorite-icon" @click="removeFavorite(favorite.recipe.id)"></i>
                </el-tooltip>
                <span class="recipe-time">{{ favorite.createTime | formatDate }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <div class="empty-state" v-if="favorites.length === 0 && !loading">
      <el-empty description="暂无收藏的菜谱"></el-empty>
      <el-button type="primary" @click="$router.push('/recipes')" style="margin-top: 20px;">去浏览菜谱</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Favorites',
  data() {
    return {
      loading: false
    }
  },
  computed: {
    ...mapGetters('favorite', ['allFavorites']),
    favorites() {
      return this.allFavorites || []
    }
  },
  filters: {
    truncate(value) {
      if (!value) return ''
      if (value.length > 50) {
        return value.substring(0, 50) + '...'
      }
      return value
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  },
  created() {
    this.fetchFavorites()
  },
  methods: {
    async fetchFavorites() {
      this.loading = true
      try {
        console.log('获取收藏列表')
        await this.$store.dispatch('favorite/fetchFavorites')
      } catch (error) {
        this.$message.error('获取收藏列表失败')
        console.error('获取收藏列表错误:', error)
      } finally {
        this.loading = false
      }
    },
    viewRecipeDetail(id) {
      this.$router.push(`/recipes/${id}`)
    },
    async removeFavorite(recipeId) {
      try {
        console.log('取消收藏:', recipeId)
        await this.$store.dispatch('favorite/removeFromFavorites', recipeId)
        this.$message.success('已取消收藏')
      } catch (error) {
        this.$message.error('操作失败，请稍后重试')
        console.error('取消收藏错误:', error)
      }
    }
  }
}
</script>

<style scoped>
.favorites {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
}

.recipe-card-col {
  margin-bottom: 20px;
}

.recipe-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s;
}

.recipe-card:hover {
  transform: translateY(-5px);
}

.recipe-image {
  height: 200px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.recipe-content {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.recipe-title {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  color: #303133;
}

.recipe-desc {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  flex: 1;
}

.recipe-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recipe-actions {
  display: flex;
  align-items: center;
}

.recipe-time {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}

.favorite-icon {
  cursor: pointer;
  font-size: 18px;
  color: #F56C6C;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}
</style>