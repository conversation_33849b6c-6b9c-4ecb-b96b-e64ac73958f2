# 菜谱管理系统

## 项目介绍

菜谱管理系统是一个全栈Web应用，用户可以浏览、搜索、收藏菜谱，也可以发布自己的菜谱。系统包含用户认证、菜谱管理、收藏功能等核心模块。

## 项目架构

该项目采用前后端分离架构：

- **前端**：Vue.js + Element UI
- **后端**：Spring Boot + MySQL

## 功能特性

### 用户功能
- 用户注册与登录
- 个人信息管理
- 修改密码
- 用户头像显示（使用昵称首字母）

### 菜谱功能
- 浏览菜谱列表
- 查看菜谱详情
- 搜索菜谱
- 创建/编辑菜谱

### 收藏功能
- 收藏/取消收藏菜谱
- 查看收藏列表

## 项目结构

```
recipe/
├── demo/               # 后端代码
│   ├── src/            # 源代码
│   ├── pom.xml         # Maven配置
│   └── README.md       # 后端说明文档
└── frontend/           # 前端代码
    ├── src/            # 源代码
    ├── public/         # 静态资源
    ├── package.json    # npm配置
    └── README.md       # 前端说明文档
```

## 快速开始

### 后端启动

```bash
cd demo
mvn spring-boot:run
```

### 前端启动

```bash
cd frontend
npm install
npm run serve
```

## 用户界面

### 未登录状态
- 导航栏显示"登录"和"注册"按钮
- 可以浏览菜谱，但无法收藏或发布菜谱

### 登录状态
- 导航栏显示用户头像（昵称首字母）
- 点击头像显示下拉菜单：个人信息、修改密码、菜谱管理、我的收藏、退出登录
- 可以收藏菜谱、发布自己的菜谱

## 技术栈

### 前端
- Vue.js 2.x
- Vue Router
- Vuex
- Element UI
- Axios

### 后端
- Spring Boot 2.7.x
- Spring Security + JWT
- Spring Data JPA
- MySQL 8.0

## 故障排除

以下是项目中解决的一些常见问题：

### 前端接口调用问题

1. **Vuex Store模块命名空间**
   - 问题：组件中没有正确使用模块命名空间，导致无法调用到后端接口
   - 解决：在访问Vuex的getters、actions时添加正确的命名空间前缀（如`recipe/fetchRecipes`）

2. **最新菜谱数据获取**
   - 问题：首页最新菜谱未正确调用专用API
   - 解决：添加`fetchLatestRecipes` action并在Home.vue中调用

3. **路由路径配置**
   - 问题：组件中的路由路径与实际路由配置不匹配
   - 解决：统一使用`/recipe/list`、`/recipe/detail/:id`、`/recipe/management`等路径格式，并添加重定向兼容旧路径
   
4. **收藏功能**
   - 问题：未使用正确的命名空间调用favorite模块的actions
   - 解决：修改为`favorite/addToFavorites`、`favorite/removeFromFavorites`等

