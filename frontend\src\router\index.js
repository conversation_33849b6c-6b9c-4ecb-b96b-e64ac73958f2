import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '../store'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { guest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue'),
    meta: { guest: true }
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('../views/test.vue')
  },
  {
    path: '/recipe/list',
    name: 'RecipeList',
    component: () => import('../views/recipe/RecipeList.vue')
  },
  {
    path: '/recipe/detail/:id',
    name: 'RecipeDetail',
    component: () => import('../views/recipe/RecipeDetail.vue')
  },
  {
    path: '/recipe/management',
    name: 'RecipeManagement',
    component: () => import('../views/recipe/RecipeManagement.vue')
  },
  // 保留旧路径，以便兼容
  {
    path: '/recipes',
    redirect: '/recipe/list'
  },
  {
    path: '/recipes/:id',
    redirect: to => {
      return { path: `/recipe/detail/${to.params.id}` }
    }
  },
  {
    path: '/recipe-management',
    redirect: '/recipe/management'
  },
  {
    path: '/favorites',
    name: 'Favorites',
    component: () => import('../views/favorite/Favorites.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/user/Profile.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('../views/user/Settings.vue'),
    meta: { requiresAuth: true }
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isLoggedIn = store.getters['auth/isLoggedIn']
  
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isLoggedIn) {
      next('/login')
    } else {
      next()
    }
  } else if (to.matched.some(record => record.meta.guest)) {
    if (isLoggedIn) {
      next('/')
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router