import { login, register, getUserInfo, updateUserInfo, changePassword, validateToken } from '@/services/auth'
import { EventBus, EVENTS } from '@/utils/eventBus'

const state = {
  token: localStorage.getItem('token') || '',
  user: JSON.parse(localStorage.getItem('user')) || null,
  status: ''
}

const getters = {
  isLoggedIn: state => !!state.token,
  authStatus: state => state.status,
  user: state => state.user,
  username: state => state.user ? state.user.username : ''
}

const actions = {
  // 登录
  async login({ commit }, user) {
    commit('auth_request')
    try {
      const resp = await login(user)
      console.log('登录API响应:', resp)
      
      // 检查响应格式，适应不同的后端返回格式
      let token, userInfo
      
      if (resp.data && resp.data.code === 200 && resp.data.data) {
        // 标准格式: { code: 200, data: { token, user } }
        token = resp.data.data.token
        userInfo = resp.data.data.user
      } else if (resp.data && resp.data.token) {
        // 简化格式: { token, user }
        token = resp.data.token
        userInfo = resp.data.user
      } else {
        throw new Error('登录响应格式不正确')
      }
      
      if (!token) {
        throw new Error('登录失败，未获取到有效token')
      }
      
      console.log('保存登录状态:', { token, userInfo })
      
      // 保存到localStorage
      localStorage.setItem('token', token)
      localStorage.setItem('user', JSON.stringify(userInfo))
      
      // 更新Vuex状态
      commit('auth_success', { token, user: userInfo })
      
      // 触发全局事件
      EventBus.$emit(EVENTS.LOGIN_SUCCESS)
      
      return resp
    } catch (err) {
      console.error('登录错误:', err)
      commit('auth_error')
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      throw err
    }
  },
  
  // 注册
  async register({ commit }, user) {
    commit('auth_request')
    try {
      const resp = await register(user)
      console.log('注册API响应:', resp)
      
      // 检查响应格式，适应不同的后端返回格式
      let token, userInfo
      
      if (resp.data && resp.data.code === 200 && resp.data.data) {
        // 标准格式: { code: 200, data: { token, user } }
        token = resp.data.data.token
        userInfo = resp.data.data.user
      } else if (resp.data && resp.data.token) {
        // 简化格式: { token, user }
        token = resp.data.token
        userInfo = resp.data.user
      } else {
        throw new Error('注册响应格式不正确')
      }
      
      if (!token) {
        throw new Error('注册失败，未获取到有效token')
      }
      
      console.log('保存注册状态:', { token, userInfo })
      
      // 保存到localStorage
      localStorage.setItem('token', token)
      localStorage.setItem('user', JSON.stringify(userInfo))
      
      // 更新Vuex状态
      commit('auth_success', { token, user: userInfo })
      
      // 触发全局事件
      EventBus.$emit(EVENTS.LOGIN_SUCCESS)
      
      return resp
    } catch (err) {
      console.error('注册错误:', err)
      commit('auth_error')
      throw err
    }
  },
  
  // 获取用户信息
  async getUserInfo({ commit }) {
    try {
      const resp = await getUserInfo()
      const userInfo = resp.data
      
      localStorage.setItem('user', JSON.stringify(userInfo))
      commit('set_user', userInfo)
      // 触发用户信息更新事件
      EventBus.$emit(EVENTS.USER_INFO_UPDATED)
      return resp
    } catch (err) {
      throw err
    }
  },
  
  // 更新用户信息
  async updateUserInfo({ commit }, userData) {
    try {
      const resp = await updateUserInfo(userData)
      const userInfo = resp.data
      
      localStorage.setItem('user', JSON.stringify(userInfo))
      commit('set_user', userInfo)
      // 触发用户信息更新事件
      EventBus.$emit(EVENTS.USER_INFO_UPDATED)
      return resp
    } catch (err) {
      throw err
    }
  },
  
  // 修改密码
  async changePassword(_, passwordData) {
    try {
      const resp = await changePassword(passwordData)
      return resp
    } catch (err) {
      throw err
    }
  },
  
  // 刷新用户信息
  async refreshUserInfo({ commit }) {
    try {
      const resp = await getUserInfo()
      const userInfo = resp.data
      
      localStorage.setItem('user', JSON.stringify(userInfo))
      commit('set_user', userInfo)
      EventBus.$emit(EVENTS.USER_INFO_UPDATED)
      return resp
    } catch (err) {
      throw err
    }
  },

  // 验证token
  async validateToken() {
    try {
      const resp = await validateToken()
      return resp
    } catch (err) {
      throw err
    }
  },
  
  // 登出
  logout({ commit }) {
    return new Promise((resolve) => {
      commit('logout')
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      // 触发登出成功事件
      EventBus.$emit(EVENTS.LOGOUT_SUCCESS)
      resolve()
    })
  },
  
  // 初始化认证状态
  initAuth({ commit }) {
    const token = localStorage.getItem('token')
    const user = localStorage.getItem('user')
    
    if (token && user) {
      try {
        const userInfo = JSON.parse(user)
        commit('auth_success', { token, user: userInfo })
      } catch (error) {
        // 如果解析失败，清除无效数据
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        commit('logout')
      }
    }
  }
}

const mutations = {
  auth_request(state) {
    state.status = 'loading'
  },
  auth_success(state, { token, user }) {
    state.status = 'success'
    state.token = token
    state.user = user
  },
  auth_error(state) {
    state.status = 'error'
    state.token = ''
    state.user = null
  },
  set_user(state, user) {
    state.user = user
  },
  logout(state) {
    state.status = ''
    state.token = ''
    state.user = null
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}