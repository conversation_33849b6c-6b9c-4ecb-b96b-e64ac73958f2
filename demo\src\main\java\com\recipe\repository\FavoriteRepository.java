package com.recipe.repository;

import com.recipe.entity.Favorite;
import com.recipe.entity.Recipe;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FavoriteRepository extends JpaRepository<Favorite, Long> {
    
    /**
     * 根据用户ID和菜谱ID查找收藏记录
     */
    Optional<Favorite> findByUserIdAndRecipeId(Long userId, Long recipeId);
    
    /**
     * 检查用户是否已收藏某个菜谱
     */
    boolean existsByUserIdAndRecipeId(Long userId, Long recipeId);
    
    /**
     * 获取用户的所有收藏（分页）
     */
    Page<Favorite> findByUserIdOrderByCreateTimeDesc(Long userId, Pageable pageable);
    
    /**
     * 获取用户收藏的菜谱列表
     */
    @Query("SELECT f.recipe FROM Favorite f WHERE f.user.id = :userId ORDER BY f.createTime DESC")
    Page<Recipe> findRecipesByUserId(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * 获取用户收藏的菜谱ID列表
     */
    @Query("SELECT f.recipe.id FROM Favorite f WHERE f.user.id = :userId")
    List<Long> findRecipeIdsByUserId(@Param("userId") Long userId);
    
    /**
     * 根据用户ID删除所有收藏
     */
    void deleteByUserId(Long userId);
    
    /**
     * 根据菜谱ID删除所有收藏
     */
    void deleteByRecipeId(Long recipeId);
    
    /**
     * 统计菜谱的收藏数量
     */
    long countByRecipeId(Long recipeId);
    
    /**
     * 统计用户的收藏数量
     */
    long countByUserId(Long userId);
}