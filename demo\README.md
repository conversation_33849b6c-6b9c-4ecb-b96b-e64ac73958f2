# 菜谱管理系统后端API

## 项目简介

这是一个基于Spring Boot开发的菜谱管理系统后端API，提供用户注册登录、菜谱管理、收藏功能等核心功能。

## 技术栈

- **框架**: Spring Boot 2.7.x
- **数据库**: MySQL 8.0
- **ORM**: Spring Data JPA
- **安全**: Spring Security + JWT
- **构建工具**: Maven
- **Java版本**: JDK 8+

## 项目结构

```
src/main/java/com/recipe/
├── RecipeApplication.java          # 启动类
├── config/
│   ├── WebConfig.java             # Web配置（跨域等）
│   └── SecurityConfig.java        # 安全配置
├── controller/
│   ├── UserController.java       # 用户相关接口
│   ├── RecipeController.java     # 菜谱相关接口
│   └── FavoriteController.java   # 收藏相关接口
├── entity/
│   ├── User.java                 # 用户实体
│   ├── Recipe.java               # 菜谱实体
│   └── Favorite.java             # 收藏实体
├── service/
│   ├── UserService.java          # 用户服务
│   ├── RecipeService.java        # 菜谱服务
│   └── FavoriteService.java      # 收藏服务
├── repository/
│   ├── UserRepository.java       # 用户数据访问
│   ├── RecipeRepository.java     # 菜谱数据访问
│   └── FavoriteRepository.java   # 收藏数据访问
├── dto/
│   ├── UserDTO.java              # 用户数据传输对象
│   ├── RecipeDTO.java            # 菜谱数据传输对象
│   └── LoginRequest.java         # 登录请求对象
└── util/
    ├── JwtUtil.java              # JWT工具类
    └── ResponseUtil.java         # 响应工具类
```

## 数据库设计

### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 菜谱表 (recipes)
```sql
CREATE TABLE recipes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    ingredients TEXT,
    steps TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 收藏表 (favorites)
```sql
CREATE TABLE favorites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    recipe_id BIGINT NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (recipe_id) REFERENCES recipes(id),
    UNIQUE KEY unique_favorite (user_id, recipe_id)
);
```

## API接口文档

### 用户相关接口

#### 用户注册
- **URL**: `POST /api/user/register`
- **请求体**:
```json
{
    "username": "testuser",
    "password": "123456",
    "email": "<EMAIL>"
}
```
- **响应**:
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>"
    }
}
```

#### 用户登录
- **URL**: `POST /api/user/login`
- **请求体**:
```json
{
    "username": "testuser",
    "password": "123456"
}
```
- **响应**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "user": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>"
        }
    }
}
```

#### 获取用户信息
- **URL**: `GET /api/user/info`
- **请求头**: `Authorization: Bearer {token}`
- **响应**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>"
    }
}
```

### 菜谱相关接口

#### 获取菜谱列表
- **URL**: `GET /api/recipes`
- **参数**: 
  - `page`: 页码（默认0）
  - `size`: 每页大小（默认10）
  - `keyword`: 搜索关键词（可选）
- **响应**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "content": [
            {
                "id": 1,
                "name": "白切鸡",
                "description": "经典粤菜，清淡鲜美",
                "imageUrl": "http://example.com/image1.jpg",
                "ingredients": "鸡肉、生姜、葱",
                "steps": "1. 准备食材 2. 煮鸡 3. 切片"
            }
        ],
        "totalElements": 1,
        "totalPages": 1,
        "number": 0,
        "size": 10
    }
}
```

#### 获取菜谱详情
- **URL**: `GET /api/recipes/{id}`
- **响应**: 同菜谱列表中的单个菜谱对象

### 收藏相关接口

#### 添加收藏
- **URL**: `POST /api/favorites`
- **请求头**: `Authorization: Bearer {token}`
- **请求体**:
```json
{
    "recipeId": 1
}
```

#### 取消收藏
- **URL**: `DELETE /api/favorites/{recipeId}`
- **请求头**: `Authorization: Bearer {token}`

#### 获取用户收藏列表
- **URL**: `GET /api/favorites`
- **请求头**: `Authorization: Bearer {token}`
- **参数**: `page`, `size`（同菜谱列表）

## 环境配置

### application.yml
```yaml
server:
  port: 8080

spring:
  datasource:
    url: ***************************************************************************************************************************
    username: root
    password: gwh842
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect

jwt:
  secret: mySecretKey
  expiration: 86400000  # 24小时
```

## 运行步骤

1. **环境准备**
   - 安装JDK 8+
   - 安装MySQL 8.0
   - 安装Maven

2. **数据库准备**
   ```sql
   CREATE DATABASE recipe_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **配置数据库连接**
   - 修改 `application.yml` 中的数据库连接信息

4. **运行项目**
   ```bash
   mvn clean install
   mvn spring-boot:run
   ```

5. **访问接口**
   - 基础URL: `http://localhost:8080`
   - API文档: `http://localhost:8080/swagger-ui.html`（如果集成了Swagger）

## 测试数据

项目启动后会自动创建一些测试数据：
- 测试用户: username=`admin`, password=`123456`
- 几道经典粤菜菜谱

## 开发说明

1. **代码规范**
   - 使用驼峰命名法
   - 类名首字母大写
   - 方法名和变量名首字母小写
   - 常量全大写，用下划线分隔

2. **异常处理**
   - 统一使用全局异常处理器
   - 自定义业务异常类
   - 返回统一的错误响应格式

3. **安全考虑**
   - 密码使用BCrypt加密
   - JWT token有效期控制
   - 输入参数验证
   - SQL注入防护


