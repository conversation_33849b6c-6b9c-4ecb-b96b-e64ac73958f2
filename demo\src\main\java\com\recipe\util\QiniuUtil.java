package com.recipe.util;

import com.google.gson.Gson;
import com.qiniu.common.QiniuException;
import com.qiniu.common.Zone;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;
import com.recipe.config.QiniuProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;

/**
 * 七牛云工具类
 */
@Component
public class QiniuUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(QiniuUtil.class);
    
    @Autowired
    private QiniuProperties qiniuProperties;
    
    /**
     * 上传文件到七牛云
     * @param file 文件
     * @return 文件访问URL
     */
    public String uploadFile(MultipartFile file) {
        try {
            // 构造一个带指定Zone对象的配置类
            Configuration cfg = new Configuration(Zone.zone0());
            UploadManager uploadManager = new UploadManager(cfg);
            
            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = "recipe_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 6) + suffix;
            String key = "images/" + fileName;
            
            // 生成上传凭证
            Auth auth = Auth.create(qiniuProperties.getAccessKey(), qiniuProperties.getSecretKey());
            String upToken = auth.uploadToken(qiniuProperties.getBucket());
            
            // 上传文件
            Response response = uploadManager.put(file.getBytes(), key, upToken);
            
            // 解析上传成功的结果
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
            
            logger.info("七牛云上传成功 - key: {}, hash: {}", putRet.key, putRet.hash);
            
            // 返回文件访问URL
            if (qiniuProperties.getDomain() != null && !qiniuProperties.getDomain().isEmpty()) {
                return "http://" + qiniuProperties.getDomain() + "/" + putRet.key;
            } else {
                // 如果没有配置域名，返回相对路径，前端需要拼接域名
                return "/" + putRet.key;
            }
            
        } catch (QiniuException ex) {
            logger.error("七牛云上传失败: {}", ex.getMessage());
            Response r = ex.response;
            try {
                logger.error("错误详情: {}", r.bodyString());
            } catch (QiniuException ex2) {
                // ignore
            }
            return null;
        } catch (IOException e) {
            logger.error("文件读取失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 删除七牛云文件
     * @param key 文件key
     * @return 是否删除成功
     */
    public boolean deleteFile(String key) {
        try {
            Configuration cfg = new Configuration(Zone.zone0());
            Auth auth = Auth.create(qiniuProperties.getAccessKey(), qiniuProperties.getSecretKey());
            com.qiniu.storage.BucketManager bucketManager = new com.qiniu.storage.BucketManager(auth, cfg);
            
            bucketManager.delete(qiniuProperties.getBucket(), key);
            logger.info("七牛云删除文件成功 - key: {}", key);
            return true;
        } catch (QiniuException ex) {
            logger.error("七牛云删除文件失败 - key: {}, error: {}", key, ex.getMessage());
            return false;
        }
    }
}