import { getRecipes, getRecipeById, getLatestRecipes } from '@/services/recipe'

const state = {
  recipes: [],
  currentRecipe: null,
  loading: false,
  // 添加分页信息
  pagination: {
    totalElements: 0,
    totalPages: 0,
    number: 0,
    size: 10
  }
}

const getters = {
  allRecipes: state => state.recipes,
  currentRecipe: state => state.currentRecipe,
  isLoading: state => state.loading,
  // 添加分页信息的getter
  pagination: state => state.pagination
}

const actions = {
  // 获取所有菜谱
  async fetchRecipes({ commit }, params = {}) {
    console.log('🔍 [Recipe Store] fetchRecipes 开始调用，参数:', params)
    commit('SET_LOADING', true)
    try {
      const response = await getRecipes(params)
      console.log('📡 [Recipe Store] API响应完整数据:', response)
      console.log('📊 [Recipe Store] response.data:', response.data)
      
      // 适配你的后端分页数据格式
      if (response.data && response.data.data && response.data.data.content) {
        // 后端返回格式: { code: 200, data: { number, size, totalPages, content: [...], totalElements }, message }
        console.log('✅ [Recipe Store] 检测到标准分页格式')
        const recipes = Array.isArray(response.data.data.content) ? response.data.data.content : []
        console.log('📝 [Recipe Store] 解析出的菜谱数据:', recipes)
        commit('SET_RECIPES', recipes)
        // 保存分页信息
        const paginationInfo = {
          totalElements: response.data.data.totalElements || 0,
          totalPages: response.data.data.totalPages || 0,
          number: response.data.data.number || 0,
          size: response.data.data.size || 10
        }
        console.log('📄 [Recipe Store] 分页信息:', paginationInfo)
        commit('SET_PAGINATION', paginationInfo)
      } else if (response.data && response.data.content) {
        // 后端返回格式: { number, size, totalPages, content: [...], totalElements }
        console.log('✅ [Recipe Store] 检测到直接分页格式')
        const recipes = Array.isArray(response.data.content) ? response.data.content : []
        console.log('📝 [Recipe Store] 解析出的菜谱数据:', recipes)
        commit('SET_RECIPES', recipes)
        // 保存分页信息
        const paginationInfo = {
          totalElements: response.data.totalElements || 0,
          totalPages: response.data.totalPages || 0,
          number: response.data.number || 0,
          size: response.data.size || 10
        }
        console.log('📄 [Recipe Store] 分页信息:', paginationInfo)
        commit('SET_PAGINATION', paginationInfo)
      } else if (response.data && response.data.data) {
        // 兼容旧格式
        console.log('⚠️ [Recipe Store] 检测到旧格式')
        const recipes = Array.isArray(response.data.data) ? response.data.data : []
        console.log('📝 [Recipe Store] 解析出的菜谱数据:', recipes)
        commit('SET_RECIPES', recipes)
      } else if (response.data && Array.isArray(response.data)) {
        // 兼容直接返回数组的情况
        console.log('⚠️ [Recipe Store] 检测到直接数组格式')
        console.log('📝 [Recipe Store] 菜谱数据:', response.data)
        commit('SET_RECIPES', response.data)
      } else {
        console.warn('❌ [Recipe Store] 未识别的数据格式，设置为空数组')
        commit('SET_RECIPES', [])
      }
    } catch (error) {
      console.error('❌ [Recipe Store] 获取菜谱列表失败:', error)
      commit('SET_RECIPES', [])
      throw error
    } finally {
      commit('SET_LOADING', false)
      console.log('🏁 [Recipe Store] fetchRecipes 调用结束')
    }
  },
  
  // 根据ID获取菜谱
  async fetchRecipeById({ commit }, id) {
    commit('SET_LOADING', true)
    try {
      const response = await getRecipeById(id)
      let recipe = null
      if (response.data && response.data.data) {
        recipe = response.data.data
      } else if (response.data) {
        recipe = response.data
      }
      commit('SET_CURRENT_RECIPE', recipe)
      return response
    } catch (error) {
      console.error('获取菜谱详情失败:', error)
      commit('SET_CURRENT_RECIPE', null)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 获取最新菜谱
  async fetchLatestRecipes({ commit }, params = {}) {
    console.log('🔍 [Recipe Store] fetchLatestRecipes 开始调用，参数:', params)
    commit('SET_LOADING', true)
    try {
      const response = await getLatestRecipes(params)
      console.log('📡 [Recipe Store] 最新菜谱API响应:', response)
      
      // 处理响应数据
      if (response.data && response.data.data && response.data.data.content) {
        // 标准格式
        const recipes = Array.isArray(response.data.data.content) ? response.data.data.content : []
        commit('SET_RECIPES', recipes)
      } else if (response.data && response.data.content) {
        // 直接返回分页格式
        const recipes = Array.isArray(response.data.content) ? response.data.content : []
        commit('SET_RECIPES', recipes)
      } else if (response.data && response.data.data) {
        // 兼容旧格式
        const recipes = Array.isArray(response.data.data) ? response.data.data : []
        commit('SET_RECIPES', recipes)
      } else if (response.data && Array.isArray(response.data)) {
        // 兼容直接返回数组
        commit('SET_RECIPES', response.data)
      } else {
        console.warn('❌ [Recipe Store] 未识别的最新菜谱数据格式，设置为空数组')
        commit('SET_RECIPES', [])
      }
    } catch (error) {
      console.error('❌ [Recipe Store] 获取最新菜谱失败:', error)
      commit('SET_RECIPES', [])
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 清除当前菜谱
  clearCurrentRecipe({ commit }) {
    commit('SET_CURRENT_RECIPE', null)
  }
}

const mutations = {
  SET_RECIPES(state, recipes) {
    console.log('🔄 [Recipe Mutation] SET_RECIPES 被调用，设置菜谱数据:', recipes)
    console.log('🔄 [Recipe Mutation] 菜谱数量:', recipes ? recipes.length : 0)
    state.recipes = recipes
    console.log('✅ [Recipe Mutation] state.recipes 已更新:', state.recipes)
  },
  SET_CURRENT_RECIPE(state, recipe) {
    console.log('🔄 [Recipe Mutation] SET_CURRENT_RECIPE 被调用:', recipe)
    state.currentRecipe = recipe
  },
  SET_LOADING(state, loading) {
    console.log('🔄 [Recipe Mutation] SET_LOADING 被调用:', loading)
    state.loading = loading
  },
  // 添加设置分页信息的mutation
  SET_PAGINATION(state, pagination) {
    console.log('🔄 [Recipe Mutation] SET_PAGINATION 被调用:', pagination)
    state.pagination = pagination
    console.log('✅ [Recipe Mutation] state.pagination 已更新:', state.pagination)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}