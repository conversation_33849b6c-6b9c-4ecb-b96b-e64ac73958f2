package com.recipe.config;

import com.recipe.entity.Recipe;
import com.recipe.entity.User;
import com.recipe.repository.RecipeRepository;
import com.recipe.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class DataInitializer implements CommandLineRunner {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private RecipeRepository recipeRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public void run(String... args) throws Exception {
        // 初始化用户数据
        initUsers();
        
        // 初始化菜谱数据
        initRecipes();
    }
    
    private void initUsers() {
        if (userRepository.count() == 0) {
            // 创建测试用户
            User user1 = new User();
            user1.setUsername("testuser");
            user1.setPassword(passwordEncoder.encode("123456"));
            user1.setEmail("<EMAIL>");
            user1.setCreateTime(LocalDateTime.now());
            userRepository.save(user1);
            
            User user2 = new User();
            user2.setUsername("admin");
            user2.setPassword(passwordEncoder.encode("admin123"));
            user2.setEmail("<EMAIL>");
            user2.setCreateTime(LocalDateTime.now());
            userRepository.save(user2);
            
            System.out.println("初始化用户数据完成");
        }
    }
    
    private void initRecipes() {
        if (recipeRepository.count() == 0) {
            // 创建示例菜谱
            Recipe recipe1 = new Recipe();
            recipe1.setName("红烧肉");
            recipe1.setDescription("经典的红烧肉做法，肥而不腻，入口即化");
            recipe1.setImageUrl("https://example.com/images/hongshaorou.jpg");
            recipe1.setIngredients("五花肉500g,生抽2勺,老抽1勺,料酒2勺,冰糖30g,葱2根,姜3片,八角2个");
            recipe1.setSteps("1. 五花肉切块，冷水下锅焯水去腥;2. 热锅下油，放入冰糖炒糖色;3. 下肉块翻炒上色;4. 加入调料和热水，大火烧开转小火炖1小时;5. 大火收汁即可");
            recipe1.setCreateTime(LocalDateTime.now());
            recipeRepository.save(recipe1);
            
            Recipe recipe2 = new Recipe();
            recipe2.setName("宫保鸡丁");
            recipe2.setDescription("四川传统名菜，酸甜微辣，鸡肉嫩滑");
            recipe2.setImageUrl("https://example.com/images/gongbaojiding.jpg");
            recipe2.setIngredients("鸡胸肉300g,花生米100g,干辣椒10个,花椒1勺,葱白2根,蒜3瓣,姜1块,生抽2勺,老抽1勺,醋1勺,糖1勺,料酒1勺,淀粉1勺");
            recipe2.setSteps("1. 鸡肉切丁用料酒和淀粉腌制;2. 花生米炸至酥脆;3. 热锅下油，爆香干辣椒和花椒;4. 下鸡丁炒至变色;5. 加入调料汁炒匀;6. 最后加入花生米和葱白炒匀即可");
            recipe2.setCreateTime(LocalDateTime.now());
            recipeRepository.save(recipe2);
            
            Recipe recipe3 = new Recipe();
            recipe3.setName("麻婆豆腐");
            recipe3.setDescription("四川经典家常菜，麻辣鲜香，豆腐嫩滑");
            recipe3.setImageUrl("https://example.com/images/mapodoufu.jpg");
            recipe3.setIngredients("嫩豆腐400g,牛肉末100g,豆瓣酱2勺,花椒粉1勺,葱花适量,蒜末1勺,姜末1勺,生抽1勺,淀粉水适量");
            recipe3.setSteps("1. 豆腐切块用盐水焯一下;2. 热锅下油，炒牛肉末至变色;3. 加入豆瓣酱炒出红油;4. 加入蒜姜末炒香;5. 加水烧开，下豆腐块;6. 用淀粉水勾芡，撒花椒粉和葱花即可");
            recipe3.setCreateTime(LocalDateTime.now());
            recipeRepository.save(recipe3);
            
            Recipe recipe4 = new Recipe();
            recipe4.setName("西红柿鸡蛋");
            recipe4.setDescription("家常经典菜，酸甜可口，营养丰富");
            recipe4.setImageUrl("https://example.com/images/xihongshijidan.jpg");
            recipe4.setIngredients("西红柿3个,鸡蛋4个,葱花适量,糖1勺,盐适量,生抽1勺");
            recipe4.setSteps("1. 鸡蛋打散炒熟盛起;2. 西红柿去皮切块;3. 热锅下油，炒西红柿出汁;4. 加入调料调味;5. 倒入炒蛋翻炒均匀;6. 撒葱花即可出锅");
            recipe4.setCreateTime(LocalDateTime.now());
            recipeRepository.save(recipe4);
            
            Recipe recipe5 = new Recipe();
            recipe5.setName("糖醋排骨");
            recipe5.setDescription("酸甜可口的经典菜品，老少皆宜");
            recipe5.setImageUrl("https://example.com/images/tangcupaigu.jpg");
            recipe5.setIngredients("排骨500g,醋3勺,糖4勺,生抽2勺,老抽1勺,料酒2勺,葱段适量,姜片适量");
            recipe5.setSteps("1. 排骨冷水下锅焯水去腥;2. 热锅下油，炸排骨至金黄;3. 留底油，下葱姜爆香;4. 倒入调料汁烧开;5. 下排骨炖煮20分钟;6. 大火收汁至浓稠即可");
            recipe5.setCreateTime(LocalDateTime.now());
            recipeRepository.save(recipe5);
            
            System.out.println("初始化菜谱数据完成");
        }
    }
}