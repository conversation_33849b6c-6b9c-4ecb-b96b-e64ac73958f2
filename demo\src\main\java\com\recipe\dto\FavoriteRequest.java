package com.recipe.dto;

public class FavoriteRequest {
    
    private Long recipeId;
    
    // 构造函数
    public FavoriteRequest() {}
    
    public FavoriteRequest(Long recipeId) {
        this.recipeId = recipeId;
    }
    
    // Getter和Setter方法
    public Long getRecipeId() {
        return recipeId;
    }
    
    public void setRecipeId(Long recipeId) {
        this.recipeId = recipeId;
    }
    
    @Override
    public String toString() {
        return "FavoriteRequest{" +
                "recipeId=" + recipeId +
                '}';
    }
}