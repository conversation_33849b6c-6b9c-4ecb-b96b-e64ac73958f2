<template>
  <div class="recipe-management">
    <div class="page-header">
      <h1>菜谱管理系统</h1>
      <el-button type="primary" @click="showAddDialog = true">新增菜谱</el-button>
    </div>
    
    <el-table :data="recipes" v-loading="loading" stripe>
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="name" label="菜谱名称" min-width="150"></el-table-column>
      <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180">
        <template slot-scope="scope">
          {{ scope.row.createTime | formatDate }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button size="mini" @click="editRecipe(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteRecipe(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 添加分页组件 -->
    <div class="pagination-wrapper" v-if="pagination.totalElements > 0">
      <el-pagination
        @current-change="handlePageChange"
        :current-page="pagination.number + 1"
        :page-size="pagination.size"
        :total="pagination.totalElements"
        layout="total, prev, pager, next, jumper">
      </el-pagination>
    </div>
    
    <!-- 新增/编辑菜谱对话框 -->
    <el-dialog 
      :title="editingRecipe ? '编辑菜谱' : '新增菜谱'"
      :visible.sync="showAddDialog"
      width="600px"
      @close="resetForm">
      <el-form :model="recipeForm" :rules="rules" ref="recipeForm" label-width="80px">
        <el-form-item label="菜谱名称" prop="name">
          <el-input v-model="recipeForm.name" placeholder="请输入菜谱名称"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            type="textarea" 
            v-model="recipeForm.description" 
            placeholder="请输入菜谱描述"
            :rows="3">
          </el-input>
        </el-form-item>
        <el-form-item label="食材" prop="ingredients">
          <el-input 
            type="textarea" 
            v-model="recipeForm.ingredients" 
            placeholder="请输入所需食材"
            :rows="3">
          </el-input>
        </el-form-item>
        <el-form-item label="制作步骤" prop="instructions">
          <el-input 
            type="textarea" 
            v-model="recipeForm.instructions" 
            placeholder="请输入制作步骤"
            :rows="5">
          </el-input>
        </el-form-item>
        <el-form-item label="详细步骤" prop="steps">
          <el-input 
            type="textarea" 
            v-model="recipeForm.steps" 
            placeholder="请输入详细制作步骤"
            :rows="4">
          </el-input>
        </el-form-item>
        <el-form-item label="菜谱图片">
          <el-upload
            class="image-uploader"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :on-success="handleImageSuccess"
            action="#"
            :http-request="handleImageUpload">
            <img v-if="recipeForm.imageUrl" :src="getImageUrl(recipeForm.imageUrl)" class="uploaded-image">
            <i v-else class="el-icon-plus image-uploader-icon"></i>
            <div class="upload-tip">点击上传图片</div>
          </el-upload>
          <div class="image-preview" v-if="recipeForm.imageUrl">
            <el-button size="mini" type="danger" @click="removeImage">删除图片</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRecipe" :loading="saving">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { createRecipe, updateRecipe, deleteRecipe as deleteRecipeApi } from '@/services/recipe'
import { uploadImage, deleteImage } from '@/services/upload'

export default {
  name: 'RecipeManagement',
  data() {
    return {
      loading: false,
      saving: false,
      showAddDialog: false,
      editingRecipe: null,
      currentPage: 1,
      recipeForm: {
        name: '',
        description: '',
        ingredients: '',
        instructions: '',
        steps: '',
        imageUrl: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入菜谱名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入菜谱描述', trigger: 'blur' }
        ],
        ingredients: [
          { required: true, message: '请输入所需食材', trigger: 'blur' }
        ],
        instructions: [
          { required: true, message: '请输入制作步骤', trigger: 'blur' }
        ],
        steps: [
          { required: true, message: '请输入详细制作步骤', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters('recipe', ['allRecipes', 'pagination']),
    recipes() {
      console.log('🔍 [RecipeManagement] recipes computed 被调用')
      const recipes = this.allRecipes || []
      console.log('📊 [RecipeManagement] 获取到的菜谱数据:', recipes)
      console.log('📊 [RecipeManagement] 菜谱数量:', recipes.length)
      return recipes
    }
  },
  filters: {
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    }
  },
  created() {
    this.fetchRecipes()
  },
  methods: {
    async fetchRecipes(page = 0) {
      console.log('🚀 [RecipeManagement] fetchRecipes 开始调用，页码:', page)
      this.loading = true
      try {
        console.log('📡 [RecipeManagement] 调用 store.dispatch fetchRecipes')
        await this.$store.dispatch('recipe/fetchRecipes', { page, size: 10 })
        console.log('✅ [RecipeManagement] store.dispatch 调用完成')
        console.log('📊 [RecipeManagement] 调用完成后的 allRecipes:', this.$store.getters['recipe/allRecipes'])
      } catch (error) {
        console.error('❌ [RecipeManagement] 获取菜谱列表失败:', error)
        this.$message.error('获取菜谱列表失败')
      } finally {
        this.loading = false
        console.log('🏁 [RecipeManagement] fetchRecipes 调用结束')
      }
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchRecipes(page - 1) // 后端页码从0开始
    },
    editRecipe(recipe) {
      this.editingRecipe = recipe
      this.recipeForm = {
        name: recipe.name,
        description: recipe.description,
        ingredients: recipe.ingredients,
        instructions: recipe.instructions || recipe.steps || '',
        steps: recipe.steps || '',
        imageUrl: recipe.imageUrl || ''
      }
      this.showAddDialog = true
    },
    async saveRecipe() {
      this.$refs.recipeForm.validate(async (valid) => {
        if (valid) {
          this.saving = true
          try {
            if (this.editingRecipe) {
              // 编辑菜谱
              await updateRecipe(this.editingRecipe.id, this.recipeForm)
              this.$message.success('菜谱更新成功')
            } else {
              // 新增菜谱
              await createRecipe(this.recipeForm)
              this.$message.success('菜谱创建成功')
            }
            this.showAddDialog = false
            this.fetchRecipes()
          } catch (error) {
            this.$message.error('操作失败，请稍后重试')
            console.error(error)
          } finally {
            this.saving = false
          }
        }
      })
    },
    async deleteRecipe(id) {
      this.$confirm('此操作将永久删除该菜谱, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteRecipeApi(id)
          this.$message.success('删除成功')
          this.fetchRecipes()
        } catch (error) {
          this.$message.error('删除失败，请稍后重试')
          console.error(error)
        }
      })
    },
    resetForm() {
      this.editingRecipe = null
      this.recipeForm = {
        name: '',
        description: '',
        ingredients: '',
        instructions: '',
        steps:'',
        imageUrl: ''
      }
      if (this.$refs.recipeForm) {
        this.$refs.recipeForm.resetFields()
      }
    },
    // 图片上传前的验证
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG/GIF 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    // 自定义图片上传处理
    async handleImageUpload(options) {
      const file = options.file
      
      try {
        // 使用统一的上传服务
        const result = await uploadImage(file)
        
        if (result.success) {
          // 上传成功，设置相对路径
          this.recipeForm.imageUrl = result.imageUrl
          this.$message.success('图片上传成功')
          options.onSuccess()
        } else {
          this.$message.error(result.error || '图片上传失败')
          options.onError()
        }
      } catch (error) {
        console.error('图片上传失败:', error)
        this.$message.error('图片上传失败，请稍后重试')
        options.onError()
      }
    },
    // 图片上传成功回调
    handleImageSuccess() {
      // 这里可以添加额外的成功处理逻辑
    },
    // 删除图片
    async removeImage() {
      if (this.recipeForm.imageUrl && this.recipeForm.imageUrl.startsWith('/images/')) {
        const fileName = this.recipeForm.imageUrl.split('/').pop()
        try {
          // 调用后端删除接口
          const result = await deleteImage(fileName)
          if (result.success) {
            this.$message.success('图片删除成功')
          } else {
            this.$message.warning('图片删除失败，但已从表单中移除')
          }
        } catch (error) {
          console.error('删除图片失败:', error)
          this.$message.warning('图片删除失败，但已从表单中移除')
        }
        // 清理localStorage中的预览数据
        localStorage.removeItem(`image_${fileName}`)
      }
      this.recipeForm.imageUrl = ''
    },
    // 获取图片URL
    getImageUrl(imageUrl) {
      if (!imageUrl) return ''
      
      // 如果是Base64格式，直接返回
      if (imageUrl.startsWith('data:image/')) {
        return imageUrl
      }
      
      // 如果是相对路径，转换为完整的后端URL
      if (imageUrl.startsWith('/images/')) {
        // 返回后端服务器的完整URL
        return `http://localhost:8080${imageUrl}`
      }
      
      // 其他情况直接返回
      return imageUrl
    }
  }
}
</script>

<style scoped>
.recipe-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
}

.dialog-footer {
  text-align: right;
}

/* 图片上传样式 */
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 200px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.image-uploader:hover {
  border-color: #409EFF;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  text-align: center;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-tip {
  font-size: 12px;
  color: #8c939d;
  margin-top: 5px;
  text-align: center;
}

.image-preview {
  margin-top: 10px;
  text-align: center;
}

.pagination-wrapper {
  margin-top: 30px;
  text-align: center;
}
</style>