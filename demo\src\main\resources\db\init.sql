-- 菜谱管理系统数据库初始化脚本
-- 适用于MySQL 8.0+

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS recipedb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE recipedb;

-- 删除已存在的表（按依赖关系顺序）
DROP TABLE IF EXISTS favorites;
DROP TABLE IF EXISTS recipes;
DROP TABLE IF EXISTS users;

-- 创建用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码（加密后）',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建菜谱表
CREATE TABLE recipes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '菜谱名称',
    description TEXT COMMENT '菜谱描述',
    image_url VARCHAR(255) COMMENT '图片URL',
    ingredients TEXT COMMENT '食材清单',
    steps TEXT COMMENT '制作步骤',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_name (name),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜谱表';

-- 创建收藏表
CREATE TABLE favorites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    recipe_id BIGINT NOT NULL COMMENT '菜谱ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    UNIQUE KEY uk_user_recipe (user_id, recipe_id),
    INDEX idx_user_id (user_id),
    INDEX idx_recipe_id (recipe_id),
    INDEX idx_create_time (create_time),
    CONSTRAINT fk_favorites_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_favorites_recipe FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收藏表';

-- 插入测试用户数据
-- 注意：密码使用BCrypt加密，这里的密码对应明文"123456"
INSERT INTO users (username, password, email) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '<EMAIL>'),
('testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '<EMAIL>'),
('chef', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '<EMAIL>');

-- 插入测试菜谱数据
INSERT INTO recipes (name, description, image_url, ingredients, steps) VALUES
('宫保鸡丁', '经典川菜，麻辣鲜香，口感丰富', 'https://example.com/images/gongbaojiding.jpg', 
 '鸡胸肉300g,花生米100g,干辣椒10个,花椒1勺,葱白2根,姜蒜适量,生抽2勺,老抽1勺,料酒1勺,糖1勺,醋1勺,盐适量,淀粉1勺', 
 '1. 鸡肉切丁用料酒、盐、淀粉腌制15分钟;2. 热锅下油，炸花生米至酥脆盛起;3. 鸡丁下锅炒至变色盛起;4. 爆香干辣椒花椒;5. 下鸡丁炒匀，调味炒制;6. 最后加入花生米炒匀即可'),

('麻婆豆腐', '四川传统名菜，麻辣鲜烫，豆腐嫩滑', 'https://example.com/images/mapodoufu.jpg',
 '嫩豆腐400g,牛肉末100g,豆瓣酱2勺,花椒粉1勺,葱花适量,姜蒜末适量,生抽1勺,料酒1勺,糖少许,淀粉水适量',
 '1. 豆腐切块用盐水焯一下;2. 热锅下油，炒牛肉末至变色;3. 加豆瓣酱炒出红油;4. 加水烧开，下豆腐块;5. 调味煮3分钟;6. 用淀粉水勾芡，撒花椒粉和葱花'),

('红烧肉', '江南名菜，色泽红亮，肥而不腻', 'https://example.com/images/hongshaorou.jpg',
 '五花肉500g,冰糖30g,生抽3勺,老抽2勺,料酒2勺,葱段3根,姜片5片,八角2个,桂皮1段',
 '1. 五花肉切块冷水下锅焯水;2. 热锅下冰糖炒糖色;3. 下肉块炒至上色;4. 加调料和香料;5. 加开水没过肉块;6. 大火烧开转小火炖1小时;7. 大火收汁即可'),

('西红柿鸡蛋', '家常菜经典，营养丰富，老少皆宜', 'https://example.com/images/xihongshijidan.jpg',
 '西红柿3个,鸡蛋4个,葱花适量,糖1勺,盐适量,生抽1勺',
 '1. 西红柿去皮切块;2. 鸡蛋打散炒熟盛起;3. 锅内留油炒西红柿出汁;4. 加糖和盐调味;5. 倒入鸡蛋炒匀;6. 撒葱花即可出锅'),

('糖醋排骨', '酸甜可口的经典菜品，老少皆宜', '/uploads/images/tangcupaigu.jpg',
 '排骨500g,醋3勺,糖4勺,生抽2勺,老抽1勺,料酒2勺,葱段适量,姜片适量',
 '1. 排骨冷水下锅焯水去腥;2. 热锅下油，炸排骨至金黄;3. 留底油，下葱姜爆香;4. 倒入调料汁烧开;5. 下排骨炖煮20分钟;6. 大火收汁至浓稠即可'),

('可乐鸡翅', '美味可口的家常菜', '/uploads/images/kelejichi.jpg', '鸡翅中8-10个,可乐1罐,姜3片,葱1段,料酒1勺,生抽2勺,老抽1勺,盐少许', '1.鸡翅洗净，两面划几刀方便入味; 2.冷水下锅，加姜片、葱段、料酒焯水后捞出洗净; 3.锅中少许油，放入鸡翅煎至两面金黄; 4.倒入可乐，没过鸡翅，加入生抽、老抽，大火烧开转小火炖煮20分钟; 5.汤汁浓稠时，大火收汁，根据口味加少许盐调味即可。'),

('红烧茄子', '经典下饭菜，咸香入味', '/uploads/images/hongshaoqiezi.jpg', '长茄子2根,青椒1个,红椒1个,蒜末适量,葱花适量,生抽2勺,老抽1勺,蚝油1勺,糖1勺,醋半勺,盐适量,淀粉1勺,水淀粉适量', '1.茄子洗净切滚刀块，加少许盐腌制10分钟出水，挤干水分后裹上薄薄一层淀粉; 2.青红椒切块; 3.锅中多放油，烧至七成热，下茄子炸至金黄变软捞出控油; 4.锅留底油，爆香葱蒜末，加入青红椒块翻炒; 5.倒入调好的酱汁（生抽、老抽、蚝油、糖、醋、盐和少量水）煮开; 6.放入炸好的茄子，快速翻炒均匀，淋入水淀粉勾芡即可。'),

('醋溜土豆丝', '酸辣爽脆，开胃小炒', '/uploads/images/culiutudousi.jpg', '土豆2个,青椒半个,干辣椒3个,花椒1小撮,蒜末适量,葱花适量,白醋2勺,生抽1勺,糖半勺,盐适量', '1.土豆去皮切细丝，用清水淘洗几遍去除多余淀粉，捞出沥干水分; 2.青椒切丝; 3.锅中热油，放入花椒、干辣椒段爆香，捞出不要; 4.放入葱蒜末炒香，倒入土豆丝快速翻炒; 5.沿锅边烹入白醋，加入生抽、糖、盐调味，继续大火快炒至土豆丝断生; 6.加入青椒丝翻炒均匀即可出锅。'),

('红烧鱼块', '鲜嫩入味，家常美味', '/uploads/images/hongshaoyukuai.jpg', '草鱼块500g,姜5片,蒜5瓣,葱2段,料酒2勺,生抽3勺,老抽1勺,糖1勺,盐适量,白胡椒粉少许,水淀粉适量', '1.鱼块洗净，加料酒、盐、白胡椒粉腌制15分钟; 2.姜切片，蒜拍扁，葱切段; 3.锅中热油，放入鱼块煎至两面金黄盛出; 4.锅留底油，爆香葱姜蒜，加入煎好的鱼块; 5.烹入料酒，加入生抽、老抽、糖和适量开水（没过鱼块），大火烧开转小火炖煮15-20分钟; 6.汤汁浓稠时，淋入水淀粉勾芡，撒上葱花即可。'),

('地三鲜', '东北经典名菜，咸鲜味美', '/uploads/images/disanxian.jpg', '土豆1个,茄子1个,青椒1个,蒜末适量,葱花适量,生抽2勺,蚝油1勺,糖半勺,盐适量,淀粉1勺,水淀粉适量', '1.土豆、茄子去皮切滚刀块，青椒切块; 2.土豆块、茄子块分别裹上薄淀粉; 3.锅中多放油，烧至七成热，分别将土豆块和茄子块炸至金黄熟透捞出控油; 4.锅留底油，爆香葱蒜末，加入青椒块翻炒; 5.倒入调好的酱汁（生抽、蚝油、糖、盐和少量水）煮开; 6.放入炸好的土豆和茄子，快速翻炒均匀，淋入水淀粉勾芡即可。'),

('鱼香肉丝', '经典川菜，酸甜辣咸鲜', '/uploads/images/yuxiangrousi.jpg', '猪里脊肉200g,水发木耳50g,胡萝卜半根,青椒半个,泡椒末1勺,姜末适量,蒜末适量,葱花适量,料酒1勺,生抽1勺,醋2勺,糖1.5勺,盐少许,淀粉1勺,水淀粉适量,豆瓣酱半勺', '1.猪里脊切丝，加料酒、盐、少许淀粉抓匀腌制10分钟; 2.木耳、胡萝卜、青椒切丝; 3.调鱼香汁：生抽、醋、糖、盐、水淀粉混合均匀; 4.热锅凉油，下肉丝滑炒至变色盛出; 5.锅留底油，爆香泡椒末、姜蒜末、豆瓣酱，炒出红油; 6.加入胡萝卜丝、木耳丝、青椒丝翻炒断生; 7.倒入肉丝，烹入鱼香汁，快速翻炒均匀，撒葱花即可。'),

('水煮牛肉', '麻辣鲜香，川菜代表', '/uploads/images/shuizhuniurou.jpg', '牛里脊300g,豆芽100g,青菜100g,干辣椒段适量,花椒适量,郫县豆瓣酱2勺,姜末蒜末适量,葱花香菜末适量,料酒1勺,生抽1勺,淀粉1勺,蛋清半个,盐少许,花椒粉辣椒粉适量', '1.牛肉切薄片，加料酒、生抽、盐、蛋清、淀粉抓匀腌制15分钟; 2.豆芽、青菜焯水铺在碗底; 3.锅中热油，爆香姜蒜末、郫县豆瓣酱，炒出红油，加入适量开水煮沸; 4.放入腌好的牛肉片，煮至变色熟透，连汤带肉倒入铺好蔬菜的碗中; 5.在牛肉上撒上干辣椒段、花椒、蒜末、花椒粉、辣椒粉; 6.另起锅烧热油，淋在调料上激出香味，撒上葱花香菜末即可。'),

('清蒸鲈鱼', '鲜嫩清淡，原汁原味', '/uploads/images/qingzhengluyu.jpg', '鲈鱼1条,葱丝适量,姜丝适量,红椒丝适量,料酒1勺,蒸鱼豉油3勺,盐少许,食用油2勺', '1.鲈鱼处理干净，两面划几刀，抹上少许盐和料酒腌制10分钟; 2.盘底铺上姜片、葱段，放上鲈鱼，鱼身上也放些葱姜丝; 3.蒸锅水开后，放入鲈鱼大火蒸8-10分钟（根据鱼大小调整时间）; 4.取出蒸好的鲈鱼，倒掉盘中多余的汤汁，去掉葱姜; 5.在鱼身上重新铺上新的葱丝、姜丝、红椒丝，淋上蒸鱼豉油; 6.锅中烧热2勺食用油，淋在葱姜红椒丝上激出香味即可。'),

('蒜蓉西兰花', '清淡爽口，营养健康', '/uploads/images/suanrongxilanhuar.jpg', '西兰花1颗,大蒜5瓣,蚝油1勺,生抽1勺,盐少许,糖少许,水淀粉适量', '1.西兰花掰成小朵，用盐水浸泡10分钟后洗净; 2.大蒜切末; 3.烧一锅开水，加入少许盐和几滴食用油，放入西兰花焯水1-2分钟至颜色翠绿捞出过凉水沥干; 4.锅中热油，爆香蒜末，加入蚝油、生抽、少许糖和少量水煮开; 5.倒入焯好的西兰花，快速翻炒均匀，淋入水淀粉勾薄芡即可。'),

('香菇滑鸡', '鲜嫩多汁，家常美味', '/uploads/images/xiangjuhuaji.jpg', '鸡腿肉2个,干香菇10朵,姜丝适量,葱段适量,料酒1勺,生抽2勺,蚝油1勺,淀粉1勺,糖少许,盐少许,麻油少许', '1.鸡腿去骨切块，加料酒、生抽、蚝油、淀粉、糖、盐、姜丝抓匀腌制20分钟; 2.干香菇用温水泡发后切片或切块; 3.将腌好的鸡块和香菇混合均匀，铺在盘中; 4.蒸锅水开后，放入鸡块大火蒸15-20分钟至熟透; 5.出锅前撒上葱段，淋少许麻油即可。'),

('木须肉', '北方经典家常菜，咸鲜可口', '/uploads/images/muxurou.jpg', '猪里脊肉150g,鸡蛋2个,水发木耳50g,黄瓜半根,胡萝卜半根,葱姜蒜末适量,料酒1勺,生抽2勺,盐适量,糖少许,淀粉1勺,水淀粉适量', '1.猪里脊切片，加料酒、盐、淀粉抓匀腌制10分钟; 2.鸡蛋打散炒熟划散盛出; 3.木耳、黄瓜、胡萝卜切片; 4.锅中热油，爆香葱姜蒜末，下肉片滑炒至变色; 5.加入胡萝卜片、木耳片翻炒，再加入黄瓜片翻炒; 6.烹入料酒、生抽、糖、盐调味，加入炒好的鸡蛋; 7.淋入水淀粉勾芡，翻炒均匀即可。');

-- 插入测试收藏数据
INSERT INTO favorites (user_id, recipe_id) VALUES
(1, 1), 
(1, 3), 
(2, 1), 
(2, 2), 
(2, 4), 
(3, 1), 
(3, 5);

-- 查询验证数据
SELECT '用户数据' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT '菜谱数据' as table_name, COUNT(*) as count FROM recipes
UNION ALL
SELECT '收藏数据' as table_name, COUNT(*) as count FROM favorites;

-- 显示表结构
SHOW CREATE TABLE users;
SHOW CREATE TABLE recipes;
SHOW CREATE TABLE favorites;

COMMIT;