<template>
  <el-dialog
    title="修改密码"
    :visible.sync="visible"
    width="400px"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="handleClose">
    
    <el-form 
      :model="passwordForm" 
      :rules="rules"
      ref="passwordForm" 
      label-width="100px"
      class="password-form">
      <el-form-item label="原密码" prop="oldPassword">
        <el-input 
          v-model="passwordForm.oldPassword" 
          type="password" 
          placeholder="请输入原密码"
          show-password>
        </el-input>
      </el-form-item>
      
      <el-form-item label="新密码" prop="newPassword">
        <el-input 
          v-model="passwordForm.newPassword" 
          type="password" 
          placeholder="请输入新密码"
          show-password>
        </el-input>
      </el-form-item>
      
      <el-form-item label="确认新密码" prop="confirmPassword">
        <el-input 
          v-model="passwordForm.confirmPassword" 
          type="password" 
          placeholder="请再次输入新密码"
          show-password>
        </el-input>
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="loading">确认修改</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'ChangePasswordDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 验证两次密码是否一致
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    
    return {
      loading: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      rules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submitForm() {
      this.$refs.passwordForm.validate(async valid => {
        if (!valid) {
          return false
        }
        
        this.loading = true
        try {
          console.log('开始调用修改密码API')
          // 调用后端API修改密码
          const response = await this.$http.post('/api/users/change-password', {
            oldPassword: this.passwordForm.oldPassword,
            newPassword: this.passwordForm.newPassword
          })
          console.log('修改密码API响应:', response)
          
          if (response.data && response.data.code === 200) {
            this.$message.success('密码修改成功')
            this.resetForm()
            this.$emit('update:visible', false)
          } else {
            this.$message.error(response.data.message || '密码修改失败')
          }
        } catch (error) {
          console.error('修改密码错误:', error)
          this.$message.error('密码修改失败，请稍后重试')
        } finally {
          this.loading = false
        }
      })
    },
    
    resetForm() {
      this.$refs.passwordForm.resetFields()
    },
    
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.password-form {
  padding: 10px 0;
}
</style> 