package com.recipe.controller;

import com.recipe.dto.LoginRequest;
import com.recipe.dto.RegisterRequest;
import com.recipe.dto.UserDTO;
import com.recipe.service.UserService;
import com.recipe.util.JwtUtil;
import com.recipe.util.ResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Map<String, Object> register(@RequestBody RegisterRequest request) {
        try {
            // 基本参数验证
            if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
                return ResponseUtil.badRequest("用户名不能为空");
            }
            if (request.getPassword() == null || request.getPassword().length() < 6) {
                return ResponseUtil.badRequest("密码长度不能少于6位");
            }
            if (request.getEmail() == null || !request.getEmail().contains("@")) {
                return ResponseUtil.badRequest("请输入有效的邮箱地址");
            }
            
            Map<String, Object> result = userService.register(request);
            return ResponseUtil.success("注册成功", result);
            
        } catch (RuntimeException e) {
            return ResponseUtil.badRequest(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("注册失败，请稍后重试");
        }
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody LoginRequest request) {
        try {
            // 基本参数验证
            if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
                return ResponseUtil.badRequest("用户名不能为空");
            }
            if (request.getPassword() == null || request.getPassword().trim().isEmpty()) {
                return ResponseUtil.badRequest("密码不能为空");
            }
            
            Map<String, Object> result = userService.login(request);
            return ResponseUtil.success("登录成功", result);
            
        } catch (RuntimeException e) {
            return ResponseUtil.unauthorized(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("登录失败，请稍后重试");
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/info")
    public Map<String, Object> getUserInfo(HttpServletRequest request) {
        try {
            String username = getUsernameFromRequest(request);
            if (username == null) {
                return ResponseUtil.unauthorized("请先登录");
            }
            
            UserDTO user = userService.getUserByUsername(username);
            return ResponseUtil.success("获取成功", user);
            
        } catch (RuntimeException e) {
            return ResponseUtil.notFound(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("获取用户信息失败");
        }
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/info")
    public Map<String, Object> updateUserInfo(HttpServletRequest request, @RequestBody Map<String, String> updateData) {
        try {
            String username = getUsernameFromRequest(request);
            if (username == null) {
                return ResponseUtil.unauthorized("请先登录");
            }
            
            UserDTO currentUser = userService.getUserByUsername(username);
            String newEmail = updateData.get("email");
            
            if (newEmail == null || !newEmail.contains("@")) {
                return ResponseUtil.badRequest("请输入有效的邮箱地址");
            }
            
            UserDTO updatedUser = userService.updateUser(currentUser.getId(), newEmail);
            return ResponseUtil.success("更新成功", updatedUser);
            
        } catch (RuntimeException e) {
            return ResponseUtil.badRequest(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("更新失败，请稍后重试");
        }
    }
    
    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public Map<String, Object> changePassword(HttpServletRequest request, @RequestBody Map<String, String> passwordData) {
        try {
            String username = getUsernameFromRequest(request);
            if (username == null) {
                return ResponseUtil.unauthorized("请先登录");
            }
            
            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");
            
            if (oldPassword == null || oldPassword.trim().isEmpty()) {
                return ResponseUtil.badRequest("请输入原密码");
            }
            if (newPassword == null || newPassword.length() < 6) {
                return ResponseUtil.badRequest("新密码长度不能少于6位");
            }
            
            userService.changePassword(username, oldPassword, newPassword);
            return ResponseUtil.success("密码修改成功");
            
        } catch (RuntimeException e) {
            return ResponseUtil.badRequest(e.getMessage());
        } catch (Exception e) {
            return ResponseUtil.error("密码修改失败，请稍后重试");
        }
    }
    
    /**
     * 验证token有效性
     */
    @GetMapping("/validate-token")
    public Map<String, Object> validateToken(HttpServletRequest request) {
        try {
            String username = getUsernameFromRequest(request);
            if (username == null) {
                return ResponseUtil.unauthorized("Token无效");
            }
            
            return ResponseUtil.success("Token有效", username);
            
        } catch (Exception e) {
            return ResponseUtil.unauthorized("Token验证失败");
        }
    }
    
    /**
     * 从请求中获取用户名
     */
    private String getUsernameFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            try {
                if (jwtUtil.isTokenValid(token)) {
                    return jwtUtil.getUsernameFromToken(token);
                }
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }
}