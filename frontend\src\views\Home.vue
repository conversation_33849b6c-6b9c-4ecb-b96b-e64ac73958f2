<template>
  <div class="home">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="welcome-banner">
          <h1>欢迎来到粤菜记忆</h1>
          <p>发现美食，分享美味，记录生活</p>
          <div class="action-buttons">
            <el-button type="primary" size="medium" @click="$router.push('/recipe/list')">浏览菜谱</el-button>
            <el-button type="success" size="medium" @click="$router.push('/recipe/management')">菜谱管理</el-button>
          </div>
          <div class="feature-description">
            <p><strong>菜谱列表</strong>：浏览所有菜谱，支持搜索和收藏功能</p>
            <p><strong>菜谱管理</strong>：管理员功能，可以新增、编辑和删除菜谱</p>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <el-divider content-position="center">最新菜谱</el-divider>
    
    <el-row :gutter="20" v-loading="loading">
      <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="recipe in latestRecipes" :key="recipe.id" class="recipe-card-col">
        <el-card :body-style="{ padding: '0px' }" shadow="hover" class="recipe-card">
          <div class="recipe-image" :style="{ backgroundImage: `url(${getImageUrl(recipe.imageUrl)})` }"></div>
          <div class="recipe-content">
            <h3 class="recipe-title">{{ recipe.name }}</h3>
            <p class="recipe-desc">{{ recipe.description | truncate }}</p>
            <div class="recipe-footer">
              <el-button type="text" @click="$router.push(`/recipe/detail/${recipe.id}`)">查看详情</el-button>
              <span class="recipe-time">{{ recipe.createTime | formatDate }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <div class="view-more" v-if="recipes.length > 0">
      <el-button type="primary" plain @click="$router.push('/recipe/list')">查看更多菜谱</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { resolveImageUrl } from '@/utils/imageUtils'

export default {
  name: 'Home',
  data() {
    return {
      loading: false
    }
  },
  computed: {
    ...mapGetters('recipe', ['allRecipes']),
    recipes() {
      return this.allRecipes || []
    },
    latestRecipes() {
      return this.recipes.slice(0, 8)
    }
  },
  filters: {
    truncate(value) {
      if (!value) return ''
      if (value.length > 50) {
        return value.substring(0, 50) + '...'
      }
      return value
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      try {
        console.log('获取最新菜谱数据')
        // 使用特定的最新菜谱API
        await this.$store.dispatch('recipe/fetchLatestRecipes')
      } catch (error) {
        this.$message.error('获取最新菜谱失败')
        console.error('获取最新菜谱错误:', error)
      } finally {
        this.loading = false
      }
    },
    // 解析图片URL
    getImageUrl(imageUrl) {
      return resolveImageUrl(imageUrl) || 'https://via.placeholder.com/300x200?text=暂无图片'
    }
  }
}
</script>

<style scoped>
.home {
  padding: 20px 0;
}

.welcome-banner {
  text-align: center;
  padding: 40px 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 30px;
}

.action-buttons {
  margin: 20px 0;
}

.action-buttons .el-button {
  margin: 0 10px;
}

.feature-description {
  margin-top: 20px;
  text-align: left;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.feature-description p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.welcome-banner h1 {
  font-size: 32px;
  margin-bottom: 15px;
  color: #303133;
}

.welcome-banner p {
  font-size: 18px;
  color: #606266;
  margin-bottom: 25px;
}

.recipe-card-col {
  margin-bottom: 20px;
}

.recipe-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s;
}

.recipe-card:hover {
  transform: translateY(-5px);
}

.recipe-image {
  height: 200px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.recipe-content {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.recipe-title {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  color: #303133;
}

.recipe-desc {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  flex: 1;
}

.recipe-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recipe-time {
  font-size: 12px;
  color: #909399;
}

.view-more {
  text-align: center;
  margin-top: 30px;
}
</style>