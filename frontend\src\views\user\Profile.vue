<template>
  <div class="profile-container">
    <div class="profile-header">
      <el-avatar :size="80" icon="el-icon-user" class="user-avatar"></el-avatar>
      <div class="user-info">
        <h2>{{ userInfo.username }}</h2>
        <p class="user-email">{{ userInfo.email }}</p>
      </div>
    </div>
    
    <el-card class="profile-card">
      <div slot="header" class="card-header">
        <span>个人信息</span>
        <el-button 
          type="primary" 
          size="small" 
          @click="editMode = !editMode"
          :icon="editMode ? 'el-icon-close' : 'el-icon-edit'"
        >
          {{ editMode ? '取消编辑' : '编辑信息' }}
        </el-button>
      </div>
      
      <el-form
        ref="profileForm"
        :model="profileForm"
        :rules="profileRules"
        label-width="100px"
        :disabled="!editMode"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="profileForm.username" 
            :disabled="true"
            placeholder="用户名不可修改"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="邮箱地址" prop="email">
          <el-input 
            v-model="profileForm.email" 
            placeholder="请输入邮箱地址"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="注册时间">
          <el-input 
            :value="formatDate(userInfo.createTime)"
            :disabled="true"
            placeholder="注册时间"
          ></el-input>
        </el-form-item>
        
        <el-form-item v-if="editMode">
          <el-button 
            type="primary" 
            @click="handleUpdateProfile"
            :loading="updateLoading"
          >
            {{ updateLoading ? '保存中...' : '保存修改' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="password-card">
      <div slot="header" class="card-header">
        <span>修改密码</span>
      </div>
      
      <el-form
        ref="passwordForm"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input 
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入原密码"
            show-password
          ></el-input>
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          ></el-input>
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            show-password
          ></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleChangePassword"
            :loading="passwordLoading"
          >
            {{ passwordLoading ? '修改中...' : '修改密码' }}
          </el-button>
          <el-button @click="resetPasswordForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Profile',
  data() {
    // 确认密码验证规则
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入新密码'))
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    
    return {
      editMode: false,
      updateLoading: false,
      passwordLoading: false,
      profileForm: {
        username: '',
        email: ''
      },
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      profileRules: {
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  
  computed: {
    ...mapGetters('auth', ['user']),
    
    userInfo() {
      return this.user || {}
    }
  },
  
  created() {
    this.initUserInfo()
    this.loadUserInfo()
  },
  
  methods: {
    // 初始化用户信息
    initUserInfo() {
      if (this.userInfo) {
        this.profileForm = {
          username: this.userInfo.username || '',
          email: this.userInfo.email || ''
        }
      }
    },
    
    // 加载用户信息
    async loadUserInfo() {
      try {
        await this.$store.dispatch('auth/getUserInfo')
        this.initUserInfo()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.$message.error('获取用户信息失败')
      }
    },
    
    // 更新个人信息
    async handleUpdateProfile() {
      try {
        await this.$refs.profileForm.validate()
        
        this.updateLoading = true
        
        await this.$store.dispatch('auth/updateUserInfo', {
          email: this.profileForm.email
        })
        
        this.$message.success('个人信息更新成功')
        this.editMode = false
        
      } catch (error) {
        console.error('更新个人信息失败:', error)
        
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(error.response.data.message)
        } else {
          this.$message.error('更新失败，请稍后重试')
        }
      } finally {
        this.updateLoading = false
      }
    },
    
    // 修改密码
    async handleChangePassword() {
      try {
        await this.$refs.passwordForm.validate()
        
        this.passwordLoading = true
        
        await this.$store.dispatch('auth/changePassword', {
          oldPassword: this.passwordForm.oldPassword,
          newPassword: this.passwordForm.newPassword
        })
        
        this.$message.success('密码修改成功')
        this.resetPasswordForm()
        
      } catch (error) {
        console.error('修改密码失败:', error)
        
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(error.response.data.message)
        } else {
          this.$message.error('密码修改失败，请稍后重试')
        }
      } finally {
        this.passwordLoading = false
      }
    },
    
    // 重置个人信息表单
    resetForm() {
      this.initUserInfo()
      this.$refs.profileForm.clearValidate()
    },
    
    // 重置密码表单
    resetPasswordForm() {
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.$refs.passwordForm.clearValidate()
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
  },
  
  watch: {
    // 监听用户信息变化
    userInfo: {
      handler() {
        this.initUserInfo()
      },
      deep: true
    },
    
    // 监听新密码变化，重新验证确认密码
    'passwordForm.newPassword'() {
      if (this.passwordForm.confirmPassword) {
        this.$refs.passwordForm.validateField('confirmPassword')
      }
    }
  }
}
</script>

<style scoped>
.profile-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 30px;
  background: linear-gradient(135deg, #409EFF 0%, #36a3f7 100%);
  border-radius: 10px;
  color: white;
}

.user-avatar {
  margin-right: 20px;
  background: rgba(255, 255, 255, 0.2);
}

.user-info h2 {
  margin: 0 0 5px;
  font-size: 24px;
  font-weight: 500;
}

.user-email {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.profile-card,
.password-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 10px;
  }
  
  .profile-header {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }
  
  .user-avatar {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>