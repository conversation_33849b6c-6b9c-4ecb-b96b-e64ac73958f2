import { 
  getFavorites, 
  addFavorite, 
  removeFavorite, 
  checkFavorite, 
  getFavoriteIds, 
  getFavoriteCount, 
  getRecipeFavoriteCount 
} from '@/services/favorite'

const state = {
  favorites: [],
  favoriteIds: [],
  favoriteCount: 0,
  loading: false,
  pagination: {
    page: 0,
    size: 10,
    totalElements: 0,
    totalPages: 0
  }
}

const getters = {
  allFavorites: state => state.favorites,
  favoriteIds: state => state.favoriteIds,
  favoriteCount: state => state.favoriteCount,
  isLoading: state => state.loading,
  pagination: state => state.pagination,
  isFavorite: state => recipeId => {
    return state.favoriteIds.includes(parseInt(recipeId)) || 
           state.favorites.some(fav => fav.recipe.id === parseInt(recipeId))
  }
}

const actions = {
  // 获取用户收藏的菜谱（支持分页）
  async fetchFavorites({ commit }, { page = 0, size = 10 } = {}) {
    commit('set_loading', true)
    try {
      const response = await getFavorites(page, size)
      // 处理分页数据结构
      const data = response.data
      if (data.content) {
        // 后端返回分页结构
        commit('set_favorites', data.content)
        commit('set_pagination', {
          page: data.page,
          size: data.size,
          totalElements: data.totalElements,
          totalPages: data.totalPages
        })
      } else {
        // 后端返回简单数组
        commit('set_favorites', data)
      }
      return response
    } catch (error) {
      throw error
    } finally {
      commit('set_loading', false)
    }
  },
  
  // 添加收藏
  async addToFavorites({ dispatch }, recipeId) {
    try {
      const response = await addFavorite(recipeId)
      // 重新获取收藏列表和ID列表
      await dispatch('fetchFavorites')
      await dispatch('fetchFavoriteIds')
      return response
    } catch (error) {
      throw error
    }
  },
  
  // 取消收藏
  async removeFromFavorites({ commit, dispatch }, recipeId) {
    try {
      await removeFavorite(recipeId)
      commit('remove_favorite', recipeId)
      // 重新获取收藏ID列表
      await dispatch('fetchFavoriteIds')
    } catch (error) {
      throw error
    }
  },
  
  // 检查是否收藏
  async checkFavoriteStatus(_, recipeId) {
    try {
      const response = await checkFavorite(recipeId)
      return response.data.isFavorited
    } catch (error) {
      throw error
    }
  },
  
  // 获取收藏ID列表
  async fetchFavoriteIds({ commit }) {
    try {
      const response = await getFavoriteIds()
      commit('set_favorite_ids', response.data)
      return response
    } catch (error) {
      throw error
    }
  },
  
  // 获取收藏数量
  async fetchFavoriteCount({ commit }) {
    try {
      const response = await getFavoriteCount()
      commit('set_favorite_count', response.data.count)
      return response
    } catch (error) {
      throw error
    }
  },
  
  // 获取菜谱被收藏数量
  async fetchRecipeFavoriteCount(_, recipeId) {
    try {
      const response = await getRecipeFavoriteCount(recipeId)
      return response.data.count
    } catch (error) {
      throw error
    }
  }
}

const mutations = {
  set_favorites(state, favorites) {
    state.favorites = favorites
  },
  
  set_favorite_ids(state, ids) {
    state.favoriteIds = ids
  },
  
  set_favorite_count(state, count) {
    state.favoriteCount = count
  },
  
  set_pagination(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination }
  },
  
  add_favorite(state, favorite) {
    state.favorites.push(favorite)
    if (favorite.recipe && favorite.recipe.id) {
      state.favoriteIds.push(favorite.recipe.id)
    }
    state.favoriteCount++
  },
  
  remove_favorite(state, recipeId) {
    const id = parseInt(recipeId)
    state.favorites = state.favorites.filter(fav => fav.recipe.id !== id)
    state.favoriteIds = state.favoriteIds.filter(favId => favId !== id)
    state.favoriteCount = Math.max(0, state.favoriteCount - 1)
  },
  
  set_loading(state, loading) {
    state.loading = loading
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}