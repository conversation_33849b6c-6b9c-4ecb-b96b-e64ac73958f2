package com.recipe.dto;

import java.time.LocalDateTime;

public class UserDTO {
    
    private Long id;
    private String username;
    private String email;
    private LocalDateTime createTime;
    
    // 构造函数
    public UserDTO() {}
    
    public UserDTO(Long id, String username, String email, LocalDateTime createTime) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.createTime = createTime;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "UserDTO{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}