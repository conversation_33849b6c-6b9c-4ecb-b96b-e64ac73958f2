# 菜谱管理系统前端

## 项目介绍

这是一个基于Vue.js开发的菜谱管理系统前端项目，提供用户注册、登录、菜谱浏览、收藏等功能。

## 技术栈

- Vue.js 2.x
- Vue Router
- Vuex
- Element UI
- Axios

## 项目结构

```
├── public/               # 静态资源
│   ├── favicon.ico       # 网站图标
│   └── index.html        # HTML模板
├── src/                  # 源代码
│   ├── assets/           # 主题、字体、图片等静态资源
│   ├── components/       # 全局公用组件
│   ├── router/           # 路由配置
│   ├── services/         # API服务
│   ├── store/            # Vuex状态管理
│   ├── utils/            # 全局公用方法
│   ├── views/            # 页面组件
│   ├── App.vue           # 入口页面
│   └── main.js           # 入口文件
├── .gitignore            # Git忽略文件
├── package.json          # 项目依赖
├── README.md             # 项目说明
└── vue.config.js         # Vue配置文件
```

## 功能模块

1. **用户模块**
   - 用户注册
   - 用户登录
   - 个人中心
   - 个人信息查看与编辑
   - 密码修改
   - 用户头像显示（使用昵称首字母）

2. **菜谱模块**
   - 菜谱列表
   - 菜谱详情

3. **收藏模块**
   - 收藏菜谱
   - 取消收藏
   - 查看收藏列表

## 用户界面特性

1. **响应式导航栏**
   - 未登录状态：显示"登录"和"注册"按钮
   - 已登录状态：显示用户头像（昵称首字母）和下拉菜单

2. **用户头像菜单**
   - 个人信息：查看和编辑个人资料
   - 修改密码：更改账户密码
   - 菜谱管理：管理自己创建的菜谱
   - 我的收藏：查看收藏的菜谱
   - 退出登录：退出当前账户

3. **个人信息弹窗**
   - 显示用户名、邮箱和注册时间
   - 支持编辑邮箱

4. **修改密码弹窗**
   - 输入原密码和新密码
   - 密码强度验证

## 安装与运行

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 构建生产环境
npm run build
```

## 后端API接口

后端API接口文档请参考项目根目录下的README.md文件。