/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
}

/* 清除浮动 */
.clearfix:after {
  content: "";
  display: block;
  clear: both;
}

/* 通用卡片样式 */
.card {
  border-radius: 4px;
  border: 1px solid #ebeef5;
  background-color: #fff;
  overflow: hidden;
  color: #303133;
  transition: 0.3s;
  margin-bottom: 20px;
}

.card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
  box-sizing: border-box;
}

.card-body {
  padding: 20px;
}

/* 通用按钮样式 */
.btn-primary {
  color: #fff;
  background-color: #409EFF;
  border-color: #409EFF;
}

.btn-success {
  color: #fff;
  background-color: #67C23A;
  border-color: #67C23A;
}

.btn-danger {
  color: #fff;
  background-color: #F56C6C;
  border-color: #F56C6C;
}

/* 通用表单样式 */
.form-item {
  margin-bottom: 22px;
}

.form-label {
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: 14px;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
}