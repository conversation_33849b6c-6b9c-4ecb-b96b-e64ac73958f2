// 图片处理工具函数

/**
 * 将Base64图片数据保存为本地文件
 * @param {string} base64Data - Base64格式的图片数据
 * @param {string} fileName - 文件名
 */
export function saveImageToLocal(base64Data, fileName) {
  try {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = base64Data
    link.download = fileName
    
    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    return true
  } catch (error) {
    console.error('保存图片失败:', error)
    return false
  }
}

/**
 * 从localStorage获取图片数据
 * @param {string} fileName - 文件名
 * @returns {string|null} - Base64图片数据或null
 */
export function getImageFromStorage(fileName) {
  try {
    const imageData = localStorage.getItem(`image_${fileName}`)
    if (imageData) {
      const parsed = JSON.parse(imageData)
      return parsed.data
    }
    return null
  } catch (error) {
    console.error('获取图片数据失败:', error)
    return null
  }
}

/**
 * 清理localStorage中的图片数据
 * @param {string} fileName - 文件名
 */
export function clearImageFromStorage(fileName) {
  try {
    localStorage.removeItem(`image_${fileName}`)
  } catch (error) {
    console.error('清理图片数据失败:', error)
  }
}

/**
 * 检查图片URL是否为本地路径，如果是则从localStorage获取数据
 * @param {string} imageUrl - 图片URL
 * @returns {string} - 实际可用的图片URL
 */
export function resolveImageUrl(imageUrl) {
  if (!imageUrl) return ''
  
  // 如果是Base64格式，直接返回
  if (imageUrl.startsWith('data:image/')) {
    return imageUrl
  }
  
  // 如果是相对路径，转换为完整的后端URL
  if (imageUrl.startsWith('/images/')) {
    return `http://localhost:8080${imageUrl}`
  }
  
  // 其他情况直接返回
  return imageUrl
}