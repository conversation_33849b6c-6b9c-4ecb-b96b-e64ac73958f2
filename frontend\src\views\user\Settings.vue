<template>
  <div class="settings-container">
    <div class="settings-header">
      <h2>个人设置</h2>
      <p>管理您的账户设置和偏好</p>
    </div>
    
    <!-- 账户安全 -->
    <el-card class="settings-card">
      <div slot="header" class="card-header">
        <i class="el-icon-lock"></i>
        <span>账户安全</span>
      </div>
      
      <div class="setting-item">
        <div class="setting-info">
          <h4>修改密码</h4>
          <p>定期更换密码可以提高账户安全性</p>
        </div>
        <el-button type="primary" @click="showChangePassword = true">
          修改密码
        </el-button>
      </div>
      
      <div class="setting-item">
        <div class="setting-info">
          <h4>登录状态</h4>
          <p>当前登录状态：{{ isLoggedIn ? '已登录' : '未登录' }}</p>
        </div>
        <el-button type="danger" @click="handleLogout">
          退出登录
        </el-button>
      </div>
    </el-card>
    
    <!-- 个人信息 -->
    <el-card class="settings-card">
      <div slot="header" class="card-header">
        <i class="el-icon-user"></i>
        <span>个人信息</span>
      </div>
      
      <div class="setting-item">
        <div class="setting-info">
          <h4>用户名</h4>
          <p>{{ userInfo.username || '未设置' }}</p>
        </div>
        <el-tag type="info">不可修改</el-tag>
      </div>
      
      <div class="setting-item">
        <div class="setting-info">
          <h4>邮箱地址</h4>
          <p>{{ userInfo.email || '未设置' }}</p>
        </div>
        <el-button type="text" @click="$router.push('/profile')">
          去修改
        </el-button>
      </div>
      
      <div class="setting-item">
        <div class="setting-info">
          <h4>注册时间</h4>
          <p>{{ formatDate(userInfo.createTime) }}</p>
        </div>
      </div>
    </el-card>
    
    <!-- 系统偏好 -->
    <el-card class="settings-card">
      <div slot="header" class="card-header">
        <i class="el-icon-setting"></i>
        <span>系统偏好</span>
      </div>
      
      <div class="setting-item">
        <div class="setting-info">
          <h4>主题模式</h4>
          <p>选择您喜欢的界面主题</p>
        </div>
        <el-select v-model="themeMode" placeholder="选择主题">
          <el-option label="浅色模式" value="light"></el-option>
          <el-option label="深色模式" value="dark"></el-option>
          <el-option label="跟随系统" value="auto"></el-option>
        </el-select>
      </div>
      
      <div class="setting-item">
        <div class="setting-info">
          <h4>语言设置</h4>
          <p>选择界面显示语言</p>
        </div>
        <el-select v-model="language" placeholder="选择语言">
          <el-option label="简体中文" value="zh-CN"></el-option>
          <el-option label="English" value="en-US"></el-option>
        </el-select>
      </div>
    </el-card>
    
    <!-- 数据管理 -->
    <el-card class="settings-card">
      <div slot="header" class="card-header">
        <i class="el-icon-document"></i>
        <span>数据管理</span>
      </div>
      
      <div class="setting-item">
        <div class="setting-info">
          <h4>清除缓存</h4>
          <p>清除本地缓存数据，可能会提高应用性能</p>
        </div>
        <el-button @click="clearCache">
          清除缓存
        </el-button>
      </div>
      
      <div class="setting-item">
        <div class="setting-info">
          <h4>导出数据</h4>
          <p>导出您的个人数据和收藏的菜谱</p>
        </div>
        <el-button type="primary" @click="exportData">
          导出数据
        </el-button>
      </div>
    </el-card>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :visible.sync="showChangePassword"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordForm"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入原密码"
            show-password
          ></el-input>
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          ></el-input>
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            show-password
          ></el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelChangePassword">取消</el-button>
        <el-button
          type="primary"
          @click="handleChangePassword"
          :loading="passwordLoading"
        >
          {{ passwordLoading ? '修改中...' : '确认修改' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Settings',
  data() {
    // 确认密码验证规则
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入新密码'))
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    
    return {
      showChangePassword: false,
      passwordLoading: false,
      themeMode: 'light',
      language: 'zh-CN',
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  
  computed: {
    ...mapGetters('auth', ['isLoggedIn', 'user']),
    
    userInfo() {
      return this.user || {}
    }
  },
  
  created() {
    this.loadSettings()
    this.loadUserInfo()
  },
  
  methods: {
    // 加载设置
    loadSettings() {
      this.themeMode = localStorage.getItem('themeMode') || 'light'
      this.language = localStorage.getItem('language') || 'zh-CN'
    },
    
    // 加载用户信息
    async loadUserInfo() {
      try {
        await this.$store.dispatch('auth/getUserInfo')
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },
    
    // 修改密码
    async handleChangePassword() {
      try {
        await this.$refs.passwordForm.validate()
        
        this.passwordLoading = true
        
        await this.$store.dispatch('auth/changePassword', {
          oldPassword: this.passwordForm.oldPassword,
          newPassword: this.passwordForm.newPassword
        })
        
        this.$message.success('密码修改成功')
        this.cancelChangePassword()
        
      } catch (error) {
        console.error('修改密码失败:', error)
        
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(error.response.data.message)
        } else {
          this.$message.error('密码修改失败，请稍后重试')
        }
      } finally {
        this.passwordLoading = false
      }
    },
    
    // 取消修改密码
    cancelChangePassword() {
      this.showChangePassword = false
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.$refs.passwordForm.clearValidate()
    },
    
    // 退出登录
    async handleLogout() {
      try {
        await this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await this.$store.dispatch('auth/logout')
        this.$message.success('已成功退出登录')
        this.$router.push('/')
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('登出失败:', error)
          this.$message.error('登出失败，请稍后重试')
        }
      }
    },
    
    // 清除缓存
    clearCache() {
      this.$confirm('清除缓存后需要重新加载页面，确定继续吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除除了认证信息外的其他缓存
        const keysToKeep = ['token', 'user']
        const keysToRemove = []
        
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (!keysToKeep.includes(key)) {
            keysToRemove.push(key)
          }
        }
        
        keysToRemove.forEach(key => localStorage.removeItem(key))
        
        this.$message.success('缓存清除成功')
        setTimeout(() => {
          window.location.reload()
        }, 1000)
      }).catch(() => {})
    },
    
    // 导出数据
    exportData() {
      this.$message.info('数据导出功能开发中，敬请期待')
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
  },
  
  watch: {
    // 监听主题模式变化
    themeMode(newVal) {
      localStorage.setItem('themeMode', newVal)
      // 这里可以添加主题切换逻辑
    },
    
    // 监听语言变化
    language(newVal) {
      localStorage.setItem('language', newVal)
      // 这里可以添加语言切换逻辑
    },
    
    // 监听新密码变化
    'passwordForm.newPassword'() {
      if (this.passwordForm.confirmPassword) {
        this.$refs.passwordForm.validateField('confirmPassword')
      }
    }
  }
}
</script>

<style scoped>
.settings-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.settings-header {
  margin-bottom: 30px;
  text-align: center;
}

.settings-header h2 {
  margin: 0 0 10px;
  font-size: 28px;
  color: #303133;
}

.settings-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.card-header i {
  margin-right: 8px;
  color: #409EFF;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #EBEEF5;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-info h4 {
  margin: 0 0 5px;
  font-size: 16px;
  color: #303133;
}

.setting-info p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    padding: 10px;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .setting-info {
    width: 100%;
  }
}
</style>