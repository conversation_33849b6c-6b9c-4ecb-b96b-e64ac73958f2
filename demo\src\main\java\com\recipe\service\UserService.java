package com.recipe.service;

import com.recipe.dto.LoginRequest;
import com.recipe.dto.RegisterRequest;
import com.recipe.dto.UserDTO;
import com.recipe.entity.User;
import com.recipe.repository.UserRepository;
import com.recipe.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    private BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    /**
     * 用户注册
     */
    public Map<String, Object> register(RegisterRequest request) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已被注册");
        }
        
        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        
        User savedUser = userRepository.save(user);
        
        // 生成JWT token
        String token = jwtUtil.generateToken(savedUser.getUsername());
        
        // 返回用户信息（不包含密码）
        UserDTO userDTO = convertToDTO(savedUser);
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", userDTO);
        
        return result;
    }
    
    /**
     * 用户登录
     */
    public Map<String, Object> login(LoginRequest request) {
        // 查找用户
        Optional<User> userOptional = userRepository.findByUsername(request.getUsername());
        if (!userOptional.isPresent()) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        User user = userOptional.get();
        
        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 生成JWT token
        String token = jwtUtil.generateToken(user.getUsername());
        
        // 返回登录信息
        UserDTO userDTO = convertToDTO(user);
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", userDTO);
        
        return result;
    }
    
    /**
     * 根据用户名获取用户信息
     */
    public UserDTO getUserByUsername(String username) {
        Optional<User> userOptional = userRepository.findByUsername(username);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("用户不存在");
        }
        
        return convertToDTO(userOptional.get());
    }
    
    /**
     * 根据用户ID获取用户信息
     */
    public UserDTO getUserById(Long userId) {
        Optional<User> userOptional = userRepository.findById(userId);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("用户不存在");
        }
        
        return convertToDTO(userOptional.get());
    }
    
    /**
     * 根据用户名获取用户实体
     */
    public User getUserEntityByUsername(String username) {
        Optional<User> userOptional = userRepository.findByUsername(username);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("用户不存在");
        }
        
        return userOptional.get();
    }
    
    /**
     * 根据用户ID获取用户实体
     */
    public User getUserEntityById(Long userId) {
        Optional<User> userOptional = userRepository.findById(userId);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("用户不存在");
        }
        
        return userOptional.get();
    }
    
    /**
     * 更新用户信息
     */
    public UserDTO updateUser(Long userId, String email) {
        Optional<User> userOptional = userRepository.findById(userId);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("用户不存在");
        }
        
        User user = userOptional.get();
        
        // 检查邮箱是否被其他用户使用
        if (!user.getEmail().equals(email) && userRepository.existsByEmail(email)) {
            throw new RuntimeException("邮箱已被其他用户使用");
        }
        
        user.setEmail(email);
        User savedUser = userRepository.save(user);
        
        return convertToDTO(savedUser);
    }
    
    /**
     * 修改密码
     */
    public void changePassword(String username, String oldPassword, String newPassword) {
        Optional<User> userOptional = userRepository.findByUsername(username);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("用户不存在");
        }
        
        User user = userOptional.get();
        
        // Verify old password
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("Original password is incorrect");
        }
        
        // Set new password
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }
    
    /**
     * 将User实体转换为UserDTO
     */
    private UserDTO convertToDTO(User user) {
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setCreateTime(user.getCreateTime());
        return dto;
    }
}