<template>
  <el-dialog
    title="个人信息"
    :visible.sync="visible"
    width="400px"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="handleClose">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
    <div v-else class="user-info-container">
      <div class="avatar-container">
        <el-avatar :size="80" :style="avatarStyle" class="user-avatar">
          {{ userInitial }}
        </el-avatar>
      </div>
      
      <el-form label-width="80px" class="user-form">
        <el-form-item label="用户名">
          <el-input v-model="userInfo.username" disabled></el-input>
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input 
            v-model="userInfo.email" 
            :disabled="!isEditing">
          </el-input>
        </el-form-item>
        <el-form-item label="注册时间" v-if="userInfo.createTime">
          <el-input v-model="formattedDate" disabled></el-input>
        </el-form-item>
      </el-form>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <template v-if="!isEditing">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="startEditing">编辑信息</el-button>
      </template>
      <template v-else>
        <el-button @click="cancelEditing">取消</el-button>
        <el-button type="primary" @click="saveUserInfo" :loading="saving">保存</el-button>
      </template>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'UserInfoDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: true,
      saving: false,
      isEditing: false,
      userInfo: {
        username: '',
        email: '',
        createTime: ''
      },
      originalUserInfo: null
    }
  },
  computed: {
    userInitial() {
      return this.userInfo.username ? this.userInfo.username.charAt(0).toUpperCase() : '?'
    },
    formattedDate() {
      if (!this.userInfo.createTime) return ''
      const date = new Date(this.userInfo.createTime)
      return date.toLocaleString()
    },
    avatarStyle() {
      // 根据用户名生成固定的颜色
      const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
      const index = this.userInfo.username ? this.userInfo.username.charCodeAt(0) % colors.length : 0
      return { backgroundColor: colors[index] }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        console.log('个人信息弹窗显示，开始获取用户信息')
        this.fetchUserInfo()
      }
    }
  },
  methods: {
    async fetchUserInfo() {
      this.loading = true
      try {
        console.log('开始调用获取用户信息API')
        // 调用后端API获取用户信息
        const response = await this.$http.get('/api/users/info')
        console.log('获取用户信息API响应:', response)
        
        if (response.data && response.data.code === 200) {
          this.userInfo = response.data.data
          console.log('用户信息:', this.userInfo)
          
          // 如果没有从API获取到用户名，尝试从localStorage获取
          if (!this.userInfo.username) {
            try {
              const userStr = localStorage.getItem('user')
              if (userStr) {
                const userData = JSON.parse(userStr)
                if (userData.username) {
                  this.userInfo.username = userData.username
                  console.log('从localStorage获取到用户名:', this.userInfo.username)
                }
              }
            } catch (e) {
              console.error('解析localStorage用户信息失败:', e)
            }
          }
          
          this.originalUserInfo = JSON.parse(JSON.stringify(this.userInfo))
        } else {
          this.$message.error(response.data.message || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息错误:', error)
        
        // 如果API调用失败，尝试从localStorage获取用户信息
        try {
          const userStr = localStorage.getItem('user')
          if (userStr) {
            const userData = JSON.parse(userStr)
            this.userInfo = {
              username: userData.username || '',
              email: userData.email || '',
              createTime: userData.createTime || new Date().toISOString()
            }
            console.log('从localStorage获取到用户信息:', this.userInfo)
            this.originalUserInfo = JSON.parse(JSON.stringify(this.userInfo))
          } else {
            this.$message.error('获取用户信息失败，请稍后重试')
          }
        } catch (e) {
          console.error('解析localStorage用户信息失败:', e)
          this.$message.error('获取用户信息失败，请稍后重试')
        }
      } finally {
        this.loading = false
      }
    },
    
    startEditing() {
      this.isEditing = true
    },
    
    cancelEditing() {
      this.userInfo = JSON.parse(JSON.stringify(this.originalUserInfo))
      this.isEditing = false
    },
    
    async saveUserInfo() {
      // 验证邮箱格式
      if (!this.userInfo.email || !this.userInfo.email.includes('@')) {
        this.$message.error('请输入有效的邮箱地址')
        return
      }
      
      this.saving = true
      try {
        console.log('开始调用更新用户信息API')
        // 调用后端API更新用户信息
        const response = await this.$http.put('/api/users/info', {
          email: this.userInfo.email
        })
        console.log('更新用户信息API响应:', response)
        
        if (response.data && response.data.code === 200) {
          this.$message.success('个人信息更新成功')
          
          // 更新localStorage中的用户信息
          try {
            const userStr = localStorage.getItem('user')
            if (userStr) {
              const userData = JSON.parse(userStr)
              userData.email = this.userInfo.email
              localStorage.setItem('user', JSON.stringify(userData))
              console.log('更新localStorage中的用户信息成功')
            }
          } catch (e) {
            console.error('更新localStorage用户信息失败:', e)
          }
          
          this.originalUserInfo = JSON.parse(JSON.stringify(this.userInfo))
          this.isEditing = false
        } else {
          this.$message.error(response.data.message || '更新失败')
        }
      } catch (error) {
        console.error('更新用户信息错误:', error)
        this.$message.error('更新失败，请稍后重试')
      } finally {
        this.saving = false
      }
    },
    
    handleClose() {
      if (this.isEditing) {
        this.cancelEditing()
      }
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.loading-container {
  padding: 20px 0;
}

.user-info-container {
  padding: 10px 0;
}

.avatar-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.user-avatar {
  background-color: #409EFF;
  color: #fff;
  font-size: 36px;
  font-weight: bold;
}

.user-form {
  margin-top: 20px;
}
</style> 