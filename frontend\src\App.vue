<template>
  <div id="app">
    <el-container>
      <el-header>
        <Header ref="header" />
      </el-header>
      <el-main>
        <router-view/>
      </el-main>
      <el-footer>
        <Footer />
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import { mapGetters } from 'vuex'
import { EventBus, EVENTS } from '@/utils/eventBus'

export default {
  name: 'App',
  components: {
    Header,
    Footer
  },
  computed: {
    ...mapGetters('auth', ['isLoggedIn', 'user'])
  },
  mounted() {
    // 初始化认证状态
    this.$store.dispatch('auth/initAuth')
    
    // 检查localStorage中是否有token，如果有则强制更新状态
    const token = localStorage.getItem('token')
    const userStr = localStorage.getItem('user')
    
    if (token && userStr) {
      try {
        const user = JSON.parse(userStr)
        console.log('App.vue: 从localStorage恢复用户状态', user)
        this.$store.commit('auth/auth_success', { token, user })
      } catch (e) {
        console.error('解析用户信息失败:', e)
      }
    }
    
    // 监听登录成功事件，强制更新整个应用
    EventBus.$on(EVENTS.LOGIN_SUCCESS, () => {
      console.log('登录成功事件触发，强制更新应用')
      this.$forceUpdate()
      // 强制更新所有子组件
      this.$nextTick(() => {
        this.$children.forEach(child => {
          if (child.$forceUpdate) {
            child.$forceUpdate()
          }
        })
      })
    })
  },
  beforeDestroy() {
    // 清理事件监听器
    EventBus.$off(EVENTS.LOGIN_SUCCESS)
  },
  watch: {
    // 监听登录状态变化，强制更新Header组件
    isLoggedIn(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.$nextTick(() => {
          if (this.$refs.header) {
            this.$refs.header.$forceUpdate()
          }
        })
      }
    },
    // 监听用户信息变化
    user: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.$nextTick(() => {
            if (this.$refs.header) {
              this.$refs.header.$forceUpdate()
            }
          })
        }
      },
      deep: true
    }
  }
}
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  min-height: 100vh;
}

.el-header {
  padding: 0;
}

.el-footer {
  padding: 20px;
  text-align: center;
  background-color: #f5f5f5;
}

.el-main {
  padding: 20px;
  min-height: calc(100vh - 120px);
}
</style>