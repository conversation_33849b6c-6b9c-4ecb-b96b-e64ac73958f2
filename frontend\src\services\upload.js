// 图片上传服务
import request from '@/utils/request'

// 上传图片到服务器
export const uploadImage = async (file) => {
  const formData = new FormData()
  
  // 生成唯一文件名
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substring(2, 8)
  const fileExtension = file.name.split('.').pop()
  const fileName = `recipe_${timestamp}_${randomStr}.${fileExtension}`
  
  formData.append('file', file)
  formData.append('fileName', fileName)
  
  try {
    // 调用后端上传接口
    const response = await request.post('/api/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return {
      success: true,
      imageUrl: response.data.imageUrl,
      fileName: fileName
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 删除图片
export const deleteImage = async (fileName) => {
  try {
    await request.delete(`/api/upload/image/${fileName}`)
    return { success: true }
  } catch (error) {
    console.error('图片删除失败:', error)
    return { success: false, error: error.message }
  }
}