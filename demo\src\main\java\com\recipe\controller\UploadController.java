package com.recipe.controller;

import com.recipe.util.QiniuUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/upload")
@CrossOrigin(origins = "*")
public class UploadController {

    @Autowired
    private QiniuUtil qiniuUtil;

    // 图片上传目录，存储到项目根目录下的uploads/images目录（备用方案）
    @Value("${upload.image.path:../uploads/images/}")
    private String uploadPath;

    @PostMapping("/image")
    public ResponseEntity<Map<String, Object>> uploadImage(
            @RequestParam("file") MultipartFile file,
            @RequestParam("fileName") String fileName) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查文件是否为空
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 检查文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                result.put("success", false);
                result.put("message", "只能上传图片文件");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 使用七牛云上传
            String imageUrl = qiniuUtil.uploadFile(file);
            
            if (imageUrl != null) {
                // 七牛云上传成功
                result.put("success", true);
                result.put("message", "上传成功");
                result.put("imageUrl", imageUrl);
                result.put("fileName", fileName);
                
                return ResponseEntity.ok(result);
            } else {
                // 七牛云上传失败，使用本地备用方案
                return uploadToLocal(file, fileName);
            }
            
        } catch (Exception e) {
            // 发生异常，使用本地备用方案
            return uploadToLocal(file, fileName);
        }
    }
    
    /**
     * 本地上传备用方案
     */
    private ResponseEntity<Map<String, Object>> uploadToLocal(MultipartFile file, String fileName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建上传目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }
            
            // 保存文件
            Path filePath = Paths.get(uploadPath, fileName);
            Files.write(filePath, file.getBytes());
            
            // 返回成功结果
            result.put("success", true);
            result.put("message", "上传成功（本地存储）");
            result.put("imageUrl", "/images/" + fileName);
            result.put("fileName", fileName);
            
            return ResponseEntity.ok(result);
            
        } catch (IOException e) {
            result.put("success", false);
            result.put("message", "文件保存失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    @DeleteMapping("/image/{fileName}")
    public ResponseEntity<Map<String, Object>> deleteImage(@PathVariable String fileName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Path filePath = Paths.get(uploadPath, fileName);
            Files.deleteIfExists(filePath);
            
            result.put("success", true);
            result.put("message", "删除成功");
            
            return ResponseEntity.ok(result);
            
        } catch (IOException e) {
            result.put("success", false);
            result.put("message", "文件删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
}